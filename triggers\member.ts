﻿// triggers/member.ts
import type { Add<PERSON>rigger, SetTrigger, RemoveTrigger } from 'blade/types';
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

 

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processMemberData = (memberData: any) => {
      console.log('Member during.add trigger - processing data:', memberData);

      // Set default values
      memberData.createdAt = memberData.createdAt || new Date();

      console.log('Member during.add trigger - processed data:', memberData);
      return memberData;
    };

    // Handle array of members
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processMemberData);
    } else {
      // Handle single member
      typedQuery.with = processMemberData(typedQuery.with);
    }

    return typedQuery;
};
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    console.log('Member during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
};
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  

    // Add any validation or cleanup logic for deletions
    console.log('Member during.remove trigger called with query:', typedQuery);
    return typedQuery;
};
