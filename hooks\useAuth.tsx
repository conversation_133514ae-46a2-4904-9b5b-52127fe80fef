// hooks/useAuth.tsx
'use client';

import { useMemo } from 'react';
import { useUnifiedSession } from '../lib/auth-client';
import { useLocation } from 'blade/hooks';
import type { UseAuthReturn, ExtendedUser} from '../lib/types';

export const useAuth = (): UseAuthReturn => {
  const { session, loading, signOut, role } = useUnifiedSession();
  const location = useLocation();

  // Memoize the enhanced user object to prevent unnecessary re-renders
  // Note: Removed location.pathname from dependencies to prevent re-renders on route changes
  const user: ExtendedUser | null = useMemo(() => {
    if (!session?.user) {
      console.log('🔍 useAuth - no session user found');
      return null;
    }

    console.log('🔍 useAuth - session user found:', {
      id: session.user.id,
      role: session.user.role,
      slug: session.user.slug
    });

    return {
      ...session.user,
      // Add utility methods to the user object
      // These methods capture location at call time, not at creation time
      isOnCorrectRoute: () => {
        if (!session.user.role) return false;
        const rolePrefix = session.user.role === 'school_admin' ? 'school' : session.user.role;
        return location.pathname.startsWith(`/${rolePrefix}`);
      },
      getCorrectDashboardUrl: () => {
        if (!session.user.role) return '/login';
        const rolePrefix = session.user.role === 'school_admin' ? 'school' : session.user.role;
        return `/${rolePrefix}/${session.user.slug}`;
      }
    } as ExtendedUser & {
      isOnCorrectRoute: () => boolean;
      getCorrectDashboardUrl: () => string;
    };
  }, [session?.user]); // Removed location.pathname dependency

  // Memoize the return object to prevent unnecessary re-renders
  return useMemo(() => ({
    user,
    session: session?.session || null,
    // Note: loading is intentionally not exposed to discourage loading states
    // Internal loading state is still used by route protection for immediate redirects
    signOut,
    role,
    isAuthenticated: !!session?.user,

    // Helper methods
    canAccessRoute: (requiredRole?: string) => {
      if (!user) return false;
      if (!requiredRole) return true;
      return user.role === requiredRole;
    },

    canAccessRoles: (allowedRoles: string[]) => {
      if (!user) return false;
      return allowedRoles.includes(user.role);
    },

    // Internal method for route protection - not intended for general use
    _getLoadingState: () => loading
  }), [user, session?.session, signOut, role, loading]);
};

export default useAuth;