import { useEffect, useRef, useState } from 'react';

declare global {
  interface Window {
    createjs: any;
  }
}

const loadCreateJS = (callback: () => void) => {
  if (window.createjs) {
    callback();
    return;
  }
  const script = document.createElement('script');
  script.src = 'https://code.createjs.com/1.0.0/createjs.min.js';
  script.onload = () => {
    callback();
  };
  document.head.appendChild(script);
};

const CanvasAnimation = () => {
  const canvasContainerRef = useRef<HTMLDivElement>(null);
  const [themeKey, setThemeKey] = useState(0);

  useEffect(() => {
    let stage: any;
    let handleResize: (curves?: any) => void;
    let observer: MutationObserver;

    const cleanup = () => {
      if (observer) observer.disconnect();
      if (window.createjs && stage) {
        window.createjs.Ticker.removeEventListener("tick", stage);
        stage.removeAllChildren();
        stage.update();
      }
      if (canvasContainerRef.current) {
        canvasContainerRef.current.innerHTML = '';
      }
      window.removeEventListener("resize", handleResize);
      stage = null;
    };

    const setupAnimation = () => {
      // Ensure cleanup runs before setting up a new animation
      if (stage) {
        if (window.createjs) {
          window.createjs.Ticker.removeEventListener("tick", stage);
          stage.removeAllChildren();
          stage.update();
        }
        stage = null;
      }
      if (canvasContainerRef.current) {
        canvasContainerRef.current.innerHTML = '';
      }

      loadCreateJS(() => {
        if (!window.createjs || !canvasContainerRef.current) return;

        const radian = 100;
        let curves: any;

        const init = () => {
          createCanvas();

          stage = new window.createjs.Stage('myCanvas');
          curves = new Curves();
          curves['x'] = window.innerWidth / 2;
          curves['y'] = window.innerHeight / 2;
          stage.addChild(curves);
          window.createjs.Ticker.addEventListener("tick", stage);
          window.createjs.Ticker.timingMode = window.createjs.Ticker.RAF;
          window.addEventListener("resize", () => handleResize(curves), false);
          handleResize(curves);
          stage.update();
        };

        const createCanvas = () => {
          if (!canvasContainerRef.current) return;
          const div = document.createElement('div');
          const canvas = document.createElement("canvas");
          canvas.setAttribute("id", "myCanvas");
          div.appendChild(canvas);
          canvasContainerRef.current.appendChild(div);

          const myCanvas = document.getElementById('myCanvas') as HTMLCanvasElement;
          myCanvas.style.width = "100%";
          myCanvas.style.height = "100%";
          myCanvas.style.display = "block";
          myCanvas.style.backgroundColor = 'transparent';
          myCanvas.style.position = "absolute";
          myCanvas.style.top = "0";
          myCanvas.style.left = "0";
        };

        // Function to get current theme colors with much more subtle contrast
        const getCurrentColors = () => {
          const isDarkMode = document.documentElement.classList.contains('dark');
          
          if (isDarkMode) {
            // Dark mode: just barely lighter colors for very subtle visibility
            const lineColor = "#2b2b2b";  // Just slightly lighter than background #101012
            const accentColor = "#2b2a2a"; // Slightly more visible
            const lineColorNoise = "#2b2b2b";
            const accentColorNoise = "#2b2a2a";
            
            console.log('CanvasAnimation - DARK MODE - lineColor:', lineColor, 'accentColor:', accentColor);
            return { lineColor, accentColor, lineColorNoise, accentColorNoise };
          } else {
            // Light mode: just barely darker colors for very subtle visibility
            const lineColor = "#d9d9d9";  // Just slightly darker than background #f2f2f2
            const accentColor = "#e3e3e3"; // Slightly more visible
            const lineColorNoise = "#e3e3e3";
            const accentColorNoise = "#d9d9d9";
            
            console.log('CanvasAnimation - LIGHT MODE - lineColor:', lineColor, 'accentColor:', accentColor);
            return { lineColor, accentColor, lineColorNoise, accentColorNoise };
          }
        };

        const { lineColor, accentColor, lineColorNoise, accentColorNoise } = getCurrentColors();

        class Line extends window.createjs.Shape {
          constructor(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number, i: number, color: string, isAccent: boolean = false) {
              super();
              
              // Add noise texture effects
              const opacity = 0.4 + (Math.random() * 0.3); // Much lower opacity range: 0.4-0.7
              
              // Choose color with noise variation
              let strokeColor = color;
              if (Math.random() > 0.7) { // 30% chance for noise variant
                if (isAccent) {
                  strokeColor = accentColorNoise;
                } else {
                  strokeColor = lineColorNoise;
                }
              }
              
              // Much thinner stroke width
              const strokeWidth = 0.1 + (Math.random() * 0.05); // Very thin: 0.1-0.15
              
              this['graphics'].beginStroke(strokeColor).setStrokeStyle(strokeWidth).moveTo(x1,y1);
              
              // Remove dash patterns for cleaner look
              
              var cmd = this['graphics'].lineTo(x1, y1).command;
              window.createjs.Tween.get(cmd, {loop: true})
              .to({x:x2, y:y2},4000, window.createjs.Ease.cubicInOut)
              .to({x:x3, y:y3}, 4000, window.createjs.Ease.cubicInOut)
              .to({x:x2, y:y2}, 4000, window.createjs.Ease.cubicInOut)
              .to({x:x3, y:y2}, 4000, window.createjs.Ease.cubicInOut)
              .to({x:x2, y:y2}, 4000, window.createjs.Ease.cubicInOut)
              .to({x:x1, y:y1}, 4000, window.createjs.Ease.cubicInOut);
              
              // Apply much lower opacity for subtlety
              this['alpha'] = opacity;
          }
          update() {}
        }

        class Curve extends window.createjs.Container {
          constructor(x: number, y: number, rad: number, reflect: number, count: number, color: string) {
              super();
              this['x'] = x*radian-radian*2;
              this['y'] = y*radian-radian*2;
              this['regX'] = radian*2;
              this['regY'] = radian*2;
              this['rotation'] = rad;
              this['scaleX'] *= reflect;

              // Determine if this is an accent color curve
              const isAccent = color === accentColor;

              for(var i = 0; i < 180; i++){
                  var x1 = radian*Math.sin(i*180/90*Math.PI/90)+radian;
                  var y1 = radian*Math.cos(i*180/90*Math.PI/90)+radian;
                  var x2 = radian/2*Math.sin((i+count)*180/45*Math.PI/90)+radian*3;
                  var y2 = radian/2*Math.cos((i+count)*180/90*Math.PI/90)+radian*3;
                  var x3 = radian/2*Math.sin(((i+45)+count)*180/45*Math.PI/90)+radian*3;
                  var y3 = radian/2*Math.cos(((i+45)+count)*180/90*Math.PI/90)+radian*3;
                  var line_inst  = new Line(x1, y1, x2, y2, x3, y3, i, color, isAccent);
                  this['addChild'](line_inst);
              }
          }
          update() {}
        }

        class Curves extends window.createjs.Container {
          constructor() {
              super();
              this['scaleX'] = this['scaleY'] = 1;

              var curve1_1 = new Curve(0, 0, 0, -1, 20, lineColor);
              this['addChild'](curve1_1);
              var curve1_2 = new Curve(0, 2, 180, 1, 12, accentColor);
              this['addChild'](curve1_2);
              var curve1_3 = new Curve(2, -2, 180, -1, 52, lineColor);
              this['addChild'](curve1_3);
              var curve1_4 = new Curve(4, -2, 180, 1, 82, accentColor);
              this['addChild'](curve1_4);
              var curve1_5 = new Curve(6, 0, 0, 1, 42, lineColor);
              this['addChild'](curve1_5);

              var curve2_1 = new Curve(0, 4, -90, -1, 62, lineColor);
              this['addChild'](curve2_1);
              var curve2_2 = new Curve(2, 4, 90, 1, 22, accentColor);
              this['addChild'](curve2_2);
              var curve2_3 = new Curve(-2, 2, 90, -1, 92, lineColor);
              this['addChild'](curve2_3);
              var curve2_4 = new Curve(-2, 0, 90, 1, 42, lineColor);
              this['addChild'](curve2_4);
              var curve2_5 = new Curve(0, -2, 270, 1, 12, lineColor);
              this['addChild'](curve2_5);

              var curve3_1 = new Curve(4, 4, -180, -1, 62, lineColor);
              this['addChild'](curve3_1);
              var curve3_2 = new Curve(4, 2, 0, 1, 22, lineColor);
              this['addChild'](curve3_2);
              var curve3_3 = new Curve(2, 6, 0, -1, 29, lineColor);
              this['addChild'](curve3_3);
              var curve3_4 = new Curve(0, 6, 0, 1, 21, lineColor);
              this['addChild'](curve3_4);
              var curve3_5 = new Curve(-2, 4, 180, 1, 100, lineColor);
              this['addChild'](curve3_5);

              var curve4_1 = new Curve(4, 0, -270, -1, 2, accentColor);
              this['addChild'](curve4_1);
              var curve4_2 = new Curve(2, 0, -90, 1, 10, lineColor);
              this['addChild'](curve4_2);
              var curve4_3 = new Curve(6, 2, -90, -1, 40, lineColor);
              this['addChild'](curve4_3);
              var curve4_4 = new Curve(6, 4, -90, 1, 140, lineColor);
              this['addChild'](curve4_4);
              var curve4_5 = new Curve(4, 6, -270, 1, 60, lineColor);
              this['addChild'](curve4_5);
              this['on']('tick', this.update, this);
          }
          update() {
              if (window.innerWidth > 768) {
                  this['scaleX'] = this['scaleY'] = window.innerWidth/1200;
              } else {
                  this['scaleX'] = this['scaleY'] = window.innerWidth/600;
              }
          }
        }

        handleResize = (curves?: any) => {
          if (!stage || !stage.canvas) return;
          stage.canvas.width = window.innerWidth;
          stage.canvas.height = window.innerHeight;
          if (curves) {
              curves['x'] = window.innerWidth / 2;
              curves['y'] = window.innerHeight / 2;
          }
          stage.update();
        };

        init();
      });
    }

    // Initial setup
    setupAnimation();

    // Observe theme changes and re-initialize animation
    const mutationCallback = (mutationsList: MutationRecord[]) => {
      for(const mutation of mutationsList) {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          cleanup();
          // Force complete re-render by updating theme key
          setThemeKey(prev => prev + 1);
        }
      }
    };

    observer = new MutationObserver(mutationCallback);
    observer.observe(document.documentElement, { attributes: true });

    return cleanup;
  }, [themeKey]);

  // Add CSS for noise texture background and overlay
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .canvas-noise-bg {
        /* Light mode: Use original background color with noise texture */
        background-color: #f2f2f2;
        
        /* Apply subtle noise texture overlay */
        background-image: 
          radial-gradient(circle at 20% 20%, rgba(0, 0, 0, 0.008) 0%, transparent 50%),
          radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
          radial-gradient(circle at 40% 60%, rgba(0, 0, 0, 0.005) 0%, transparent 30%);
      }
      
      .dark .canvas-noise-bg {
        /* Dark mode: Use original background color with noise texture */
        background-color: #101012;
        
        /* Dark mode noise texture overlay */
        background-image: 
          radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.012) 0%, transparent 50%),
          radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.006) 0%, transparent 50%),
          radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.009) 0%, transparent 30%);
      }
      
      .canvas-noise-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: -9;
        opacity: 0.4;
        
        /* Subtle animated grain for light mode */
        background-image: 
          radial-gradient(circle at 25% 25%, rgba(0, 0, 0, 0.01) 0%, transparent 1px),
          radial-gradient(circle at 75% 75%, rgba(0, 0, 0, 0.005) 0%, transparent 1px),
          radial-gradient(circle at 50% 10%, rgba(255, 255, 255, 0.08) 0%, transparent 1px),
          radial-gradient(circle at 10% 90%, rgba(0, 0, 0, 0.008) 0%, transparent 1px);
        background-size: 120px 120px, 180px 180px, 100px 100px, 150px 150px;
        animation: noise-shift 30s infinite linear;
      }
      
      @keyframes noise-shift {
        0% { transform: translate(0, 0); }
        25% { transform: translate(-0.5px, 0.5px); }
        50% { transform: translate(0.5px, -0.5px); }
        75% { transform: translate(-0.3px, -0.3px); }
        100% { transform: translate(0, 0); }
      }
      
      .dark .canvas-noise-overlay {
        /* Dark mode grain */
        background-image: 
          radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.006) 0%, transparent 1px),
          radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.003) 0%, transparent 1px),
          radial-gradient(circle at 50% 10%, rgba(255, 255, 255, 0.004) 0%, transparent 1px),
          radial-gradient(circle at 10% 90%, rgba(255, 255, 255, 0.002) 0%, transparent 1px);
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return (
    <>
      <div ref={canvasContainerRef} className="fixed top-0 left-0 w-full h-full -z-10 canvas-noise-bg" />
      <div className="canvas-noise-overlay" />
    </>
  );
};

export default CanvasAnimation;