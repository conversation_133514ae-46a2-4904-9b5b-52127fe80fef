// components/optimization/DataOptimizer.server.tsx
import { use, useBatch } from 'blade/server/hooks';

interface OptimizedDataProps {
  teacherId: string;
  children: (data: {
    teacherData: any;
    studentsData: any[];
    classesData: any[];
    gradeLevelsData: any[];
  }) => React.ReactNode;
}

/**
 * Server component that batches all teacher-related queries into a single transaction
 * This prevents the excessive revalidation you're experiencing
 */
export function OptimizedDataProvider({ teacherId, children }: OptimizedDataProps) {
  // Batch ALL teacher queries into a single transaction
  // This is the key to preventing excessive revalidation
  const [teacherData, studentsData, classesData, gradeLevelsData] = useBatch(() => [
    // Teacher profile data
    use.users({
      with: { 
        teacherSubjects: { with: { subject: true } }
      },
      where: { id: teacherId, role: 'teacher' }
    }),
    
    // Students connected to this teacher
    use.studentTeachers({
      with: {
        student: { 
          with: { 
            user: true,
            studentClasses: { with: { class: true } }
          } 
        }
      },
      where: { teacherId, status: 'active' }
    }),
    
    // Classes for this teacher
    use.classes({
      with: { 
        studentClasses: { with: { student: { with: { user: true } } } },
        gradeLevel: true,
        subject: true
      },
      where: { teacherId, isActive: true }
    }),
    
    // Grade levels for this teacher
    use.gradeLevels({
      where: { teacherId, isActive: true },
      orderedBy: { ascending: ['sortOrder', 'name'] }
    })
  ]);

  // Transform the data for easier consumption
  const transformedData = {
    teacherData: teacherData[0] || null,
    studentsData: studentsData.map(st => ({
      ...st.student,
      assignedAt: st.assignedAt,
      status: st.status
    })),
    classesData,
    gradeLevelsData
  };

  return children(transformedData);
}