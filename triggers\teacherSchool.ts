﻿// triggers/teacherSchool.ts
import type { AddTrigger, SetTrigger, RemoveTrigger } from 'blade/types';
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;


    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processTeacherSchoolData = (relationshipData: any) => {
      console.log('TeacherSchool during.add trigger - processing data:', relationshipData);

      // Set default values
      relationshipData.status = relationshipData.status || 'active';
      relationshipData.invitedAt = relationshipData.invitedAt || new Date();

      console.log('TeacherSchool during.add trigger - processed data:', relationshipData);
      return relationshipData;
    };

    // Handle array of relationships
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processTeacherSchoolData);
    } else {
      // Handle single relationship
      typedQuery.with = processTeacherSchoolData(typedQuery.with);
    }

    return typedQuery;
};
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;



    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // Set join timestamp if status is being changed to active
    if (typedQuery.to.status === 'active' && !typedQuery.to.joinedAt) {
      typedQuery.to.joinedAt = new Date();
    }

    console.log('TeacherSchool during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
};
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;


    // Add any validation or cleanup logic for deletions
    console.log('TeacherSchool during.remove trigger called with query:', typedQuery);
    return typedQuery;
};
