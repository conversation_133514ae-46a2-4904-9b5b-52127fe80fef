﻿// triggers/teacher.ts
import type { AddTrigger, SetTrigger, RemoveTrigger } from 'blade/types';
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;



    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processTeacherData = (teacherData: any) => {
      console.log('Teacher during.add trigger - processing data:', teacherData);

      // Set default values
      teacherData.bio = teacherData.bio || '';
      teacherData.isIndependent = teacherData.isIndependent !== false; // Default to true
      teacherData.isVerified = teacherData.isVerified || false;
      teacherData.createdAt = teacherData.createdAt || new Date();
      teacherData.updatedAt = teacherData.updatedAt || new Date();

      console.log('Teacher during.add trigger - processed data:', teacherData);
      return teacherData;
    };

    // Handle array of teachers
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processTeacherData);
    } else {
      // Handle single teacher
      typedQuery.with = processTeacherData(typedQuery.with);
    }

    return typedQuery;
};
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;



    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // Update timestamp
    typedQuery.to.updatedAt = new Date();

    console.log('Teacher during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
};
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;


    // Add any validation or cleanup logic for deletions
    console.log('Teacher during.remove trigger called with query:', typedQuery);
    return typedQuery;
};
