//grade-level-management-dialog-simple.client.tsx
'use client';

import { useState, useCallback, useRef, useEffect, useMemo, forwardRef } from 'react';
import { useAuth } from '../../../../hooks/useAuth';
import { useMutation } from 'blade/client/hooks';
import { Dialog } from '@base-ui-components/react/dialog';
import { Switch } from '../../../animate-ui/base/switch';
import { GraduationCap, X, Plus } from 'lucide-react';
import { cn } from '../../../../lib/utils';

interface GradeLevelManagementDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export const GradeLevelManagementDialog = forwardRef<HTMLDivElement, GradeLevelManagementDialogProps>(({ isOpen, onOpenChange }, ref) => {
  const { user } = useAuth();
  const { add } = useMutation();

  // Memoize user ID to prevent re-renders when user image changes
  const userId = useMemo(() => user?.id, [user?.id]);
  
  // Simple form state - just class names
  const [classInput, setClassInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error'>('success');
  const [showAdvanced, setShowAdvanced] = useState(false);
  const messageTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const resetForm = useCallback(() => {
    setClassInput('');
    setMessage('');
    setShowAdvanced(false);
    setIsLoading(false);
    if (messageTimeoutRef.current) {
      clearTimeout(messageTimeoutRef.current);
      messageTimeoutRef.current = null;
    }
  }, []);

  // Reset form when dialog opens
  useEffect(() => {
    if (isOpen) {
      resetForm();
    }
  }, [isOpen, resetForm]);

  const showMessage = useCallback((msg: string, type: 'success' | 'error') => {
    setMessage(msg);
    setMessageType(type);
    
    if (messageTimeoutRef.current) {
      clearTimeout(messageTimeoutRef.current);
    }
    
    messageTimeoutRef.current = setTimeout(() => {
      setMessage('');
      messageTimeoutRef.current = null;
    }, 4000);
  }, []);

  const handleCreateClasses = async () => {
    if (!classInput.trim()) {
      showMessage('Please enter at least one class name', 'error');
      return;
    }

    if (!userId) {
      showMessage('User not authenticated', 'error');
      return;
    }

    setIsLoading(true);

    try {
      // Split input by commas or new lines and clean up
      const classNames = classInput
        .split(/[,\n]/)
        .map(name => name.trim())
        .filter(name => name.length > 0);

      if (classNames.length === 0) {
        showMessage('Please enter valid class names', 'error');
        setIsLoading(false);
        return;
      }

      // Create grade levels for each class
      const gradePromises = classNames.map(async (className) => {
        return add.gradeLevel({
          with: {
            name: className,
            code: className.toLowerCase().replace(/\s+/g, '-'),
            description: `${className} class`,
            category: 'academic',
            educationType: 'traditional_academic',
            sortOrder: 0,
            teacherId: userId,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        });
      });

      await Promise.all(gradePromises);

      showMessage(`Successfully created ${classNames.length} class${classNames.length > 1 ? 'es' : ''}!`, 'success');
      
      // Close dialog after success
      setTimeout(() => {
        onOpenChange(false);
      }, 1500);

    } catch (error) {
      console.error('Error creating classes:', error);
      showMessage('Failed to create classes. Please try again.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = useCallback(() => {
    onOpenChange(false);
  }, [onOpenChange]);

  if (!isOpen) return null;

  return (
    <Dialog.Root open={isOpen} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Backdrop className="fixed inset-0 bg-black/60 backdrop-blur-sm z-[100002]" />
        <Dialog.Popup
        ref={ref}
        className="fixed top-1/2 left-1/2 -mt-8 w-[96vw] md:max-w-[500px] max-w-[calc(100vw-3rem)] -translate-x-1/2 -translate-y-1/2 rounded-lg bg-gradient-to-r from-[#f2f2f2] via-[#e8e8e8] to-[#eeeeee] dark:from-[#101012] dark:via-[#18181a] dark:to-[#171719] text-gray-900 dark:text-gray-100 outline-1 outline-black/10 dark:outline-white/10 transition-all duration-150 data-[ending-style]:scale-90 data-[ending-style]:opacity-0 data-[starting-style]:scale-90 data-[starting-style]:opacity-0 z-[100003] max-h-[85vh] overflow-hidden flex flex-col"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30">
              <GraduationCap className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <Dialog.Title className="text-lg font-semibold">Add Classes</Dialog.Title>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                What classes do you teach?
              </p>
            </div>
          </div>
          <Dialog.Close asChild>
            <button
              onClick={handleClose}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </Dialog.Close>
        </div>

        {/* Content */}
        <div className="flex-1 p-6 space-y-6">
          {/* Simple Class Input */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Class Names
            </label>
            <textarea
              value={classInput}
              onChange={(e) => setClassInput(e.target.value)}
              placeholder="Enter your classes, separated by commas or new lines:&#10;&#10;Math 101&#10;English Literature&#10;Science Basics"
              className="w-full h-32 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              disabled={isLoading}
            />
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Separate multiple classes with commas or put each on a new line
            </p>
          </div>

          {/* Advanced Options Toggle */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Advanced Options
            </span>
            <Switch
              checked={showAdvanced}
              onCheckedChange={setShowAdvanced}
              disabled={isLoading}
            />
          </div>

          {/* Advanced Options (Hidden by default) */}
          {showAdvanced && (
            <div className="space-y-4 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Advanced options coming soon...
              </p>
            </div>
          )}

          {/* Message */}
          {message && (
            <div className={cn(
              "p-3 rounded-lg text-sm font-medium",
              messageType === 'success' 
                ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-800"
                : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 border border-red-200 dark:border-red-800"
            )}>
              {message}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={handleClose}
            disabled={isLoading}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={handleCreateClasses}
            disabled={isLoading || !classInput.trim()}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 rounded-lg transition-colors disabled:opacity-50 flex items-center gap-2"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <Plus className="w-4 h-4" />
                Create Classes
              </>
            )}
          </button>
        </div>
        </Dialog.Popup>
      </Dialog.Portal>
    </Dialog.Root>
  );
});

GradeLevelManagementDialog.displayName = 'GradeLevelManagementDialog';
