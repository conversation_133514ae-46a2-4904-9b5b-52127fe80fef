﻿// triggers/educationalContext.ts
import type { Add<PERSON><PERSON>ger, SetTrigger, RemoveTrigger } from 'blade/types';
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processEducationalContextData = (contextData: any) => {
      console.log('Educational Context during.add trigger - processing data:', contextData);

      // Ensure required fields
      contextData.isActive = contextData.isActive !== false; // Default to true
      contextData.createdAt = new Date();
      contextData.updatedAt = new Date();

      console.log('Educational Context during.add trigger - processed data:', contextData);
      return contextData;
    };

    // Handle array of contexts
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processEducationalContextData);
    } else {
      // Handle single context
      typedQuery.with = processEducationalContextData(typedQuery.with);
    }

    return typedQuery;
};
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

   

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // Update timestamp
    typedQuery.to.updatedAt = new Date();

    console.log('Educational Context during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
};
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

   

    // Add any validation or cleanup logic for deletions
    console.log('Educational Context during.remove trigger called with query:', typedQuery);
    return typedQuery;
};
