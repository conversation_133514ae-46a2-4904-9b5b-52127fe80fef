# PowerShell script to fix all trigger files to use correct Blade trigger structure
# Convert from 'during' object to individual exports as per Blade documentation

$triggerFiles = Get-ChildItem -Path "triggers" -Filter "*.ts" | Where-Object { $_.Name -ne "index.ts" }

Write-Host "Converting trigger files to correct Blade structure..."
Write-Host "================================================================"

foreach ($file in $triggerFiles) {
    $filePath = $file.FullName
    $fileName = $file.Name
    
    Write-Host "`nProcessing $fileName..."
    
    # Read the file content
    $content = Get-Content -Path $filePath -Raw
    
    # Skip if already converted (doesn't have 'during' object)
    if ($content -notmatch "export const during = \{") {
        Write-Host "  Already converted, skipping..."
        continue
    }
    
    # Extract the model name from filename
    $modelName = $fileName -replace "\.ts$", ""
    $capitalizedModel = (Get-Culture).TextInfo.ToTitleCase($modelName)
    
    Write-Host "  Converting $modelName triggers..."
    
    # Start building new content
    $newContent = @"
// triggers/$fileName
import type { AddTrigger, SetTrigger, RemoveTrigger } from 'blade/types';

"@
    
    # Extract the add trigger logic from during object
    if ($content -match "add: \([^{]*\) => \{([^}]+(?:\{[^}]*\}[^}]*)*)\}") {
        $addLogic = $matches[1]
        # Clean up the logic - remove the const [query, _multiple, _options] = args; line
        $addLogic = $addLogic -replace "const \[query, _multiple, _?options\] = args;", ""
        $addLogic = $addLogic -replace "const typedQuery = query as any;", ""
        $addLogic = $addLogic -replace "typedQuery", "query"
        
        $newContent += @"
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;
$addLogic
};

"@
    }
    
    # Extract the set trigger logic from during object
    if ($content -match "set: \([^{]*\) => \{([^}]+(?:\{[^}]*\}[^}]*)*)\}") {
        $setLogic = $matches[1]
        # Clean up the logic
        $setLogic = $setLogic -replace "const \[query, _multiple, _?options\] = args;", ""
        $setLogic = $setLogic -replace "const typedQuery = query as any;", ""
        $setLogic = $setLogic -replace "typedQuery", "query"
        
        $newContent += @"
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;
$setLogic
};

"@
    }
    
    # Extract the remove trigger logic from during object
    if ($content -match "remove: \([^{]*\) => \{([^}]+(?:\{[^}]*\}[^}]*)*)\}") {
        $removeLogic = $matches[1]
        # Clean up the logic
        $removeLogic = $removeLogic -replace "const \[query, _multiple, _?options\] = args;", ""
        $removeLogic = $removeLogic -replace "const typedQuery = query as any;", ""
        $removeLogic = $removeLogic -replace "typedQuery", "query"
        
        $newContent += @"
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;
$removeLogic
};
"@
    }
    
    # Write the new content to the file
    Set-Content -Path $filePath -Value $newContent -Encoding UTF8
    Write-Host "  ✅ Converted $fileName to individual exports"
}

Write-Host "`n================================================================"
Write-Host "✅ All trigger files converted to correct Blade structure!"
Write-Host "================================================================"
