'use client';
import { ComponentProps, memo } from "react";
import { cn } from "../../../../lib/utils";
import { useIsMobile } from "../../../../hooks/use-mobile";
import { useSharedSidebarState } from "../../../../stores/sidebar-store.client";

import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarFooter,
} from "../../../ui/sidebar.client";
import { Sheet, SheetContent, SheetTitle } from "../../../ui/sheet.client";

// FIXED: Add proper interface for sidebar state
interface SidebarState {
  isRightSidebarOpen: boolean;
  rightSidebarContent: string | null;
  closeRightSidebar: () => void;
  [key: string]: any;
}

interface RightSidebarProps extends ComponentProps<"div"> {
  sidebarState?: SidebarState;
  showFlyoutControl?: boolean;
  className?: string;
  side?: "left" | "right";
  content?: string | null;
}



export const RightSidebar = memo(function RightSidebar({ sidebarState, showFlyoutControl = true, className, content }: RightSidebarProps) {
  const isMobile = useIsMobile();
  
  // Get sidebar state from hooks if not provided via props
  const hookState = useSharedSidebarState();
  
  // Use sidebarState from props or fallback to hooks
  const { isRightSidebarOpen, rightSidebarContent, closeRightSidebar } = sidebarState || {
    isRightSidebarOpen: hookState.isRightSidebarOpen,
    rightSidebarContent: content || hookState.rightSidebarContent,
    closeRightSidebar: hookState.closeRightSidebar,
  };
  
  // Use the actual content from the store
  const actualContent = rightSidebarContent || hookState.rightSidebarContent;

  // Early return if not open - right sidebar is now disabled by default
  if (!isRightSidebarOpen) {
    return null;
  }

  // Placeholder content for future use
  return (
    <div className={cn("flex flex-col h-full w-fit relative", className)}>
      {isMobile ? (
        <Sheet 
          open={isRightSidebarOpen} 
          onOpenChange={(open) => !open && closeRightSidebar()} 
          modal={true}
        >
          <SheetContent
            side="right"
            hideClose={true}
            className={cn(
              "border-l z-[99999] p-0 flex flex-col dark:bg-[#171719] overflow-hidden",
              "w-screen sm:w-[350px] h-screen rounded-none"
            )}
          >
            <SidebarHeader className="border-b border-black/20 dark:border-white/20 flex-shrink-0">
              <div className="px-3 py-2 md:py-4 flex items-center justify-between min-h-[52px] md:min-h-[64px]">
                <h2 className="text-xl font-manrope_1 text-left font-semibold text-black dark:text-white">
                  Available for Future Use
                </h2>
              </div>
            </SidebarHeader>
            
            <SidebarContent className="flex-1 overflow-y-auto overflow-x-hidden">
              <div className="h-full flex items-center justify-center">
                <p className="text-gray-500">This sidebar is available for future features</p>
              </div>
            </SidebarContent>
          </SheetContent>
        </Sheet>
      ) : (
        <div
          className={cn(
            "fixed top-0 right-0 pt-12 h-full z-[99999] flex flex-col",
            "bg-[#eeeeee] dark:bg-[#171719]",
            "transition-transform duration-100 ease-out",
            "w-[350px] rounded-tl-xl",
            isRightSidebarOpen ? "translate-x-0" : "translate-x-full"
          )}
        >
          <div className="border-b border-black/20 dark:border-white/20 flex-shrink-0">
            <div className="px-3 py-2 md:py-4 flex items-center justify-between min-h-[52px] md:min-h-[64px]">
              <h2 className="text-xl font-manrope_1 text-left font-semibold text-black dark:text-white">
                Available for Future Use
              </h2>
            </div>
          </div>
          
          <div className="flex-1 overflow-y-auto overflow-x-hidden">
            <div className="h-full flex items-center justify-center">
              <p className="text-gray-500">This sidebar is available for future features</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});
