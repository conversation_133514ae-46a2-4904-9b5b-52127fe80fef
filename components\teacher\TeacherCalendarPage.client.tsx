// components/teacher/TeacherCalendarPage.client.tsx
'use client';

import { useParams } from 'blade/hooks';
import { useAuth } from '../../hooks/useAuth';

const TeacherCalendarPage = () => {
  const { slug } = useParams();
  const { user } = useAuth();
  
  // Verify the slug matches the current user
  if (user && user.slug !== slug) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-gray-600">You can only access your own profile.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-2">
      <h1 className="text-3xl font-bold mb-6">Notifications</h1>
      <div className="space-y-4">
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                New student enrollment
              </h3>
              <p className="mt-1 text-sm text-blue-700 dark:text-blue-300">
                Sarah Johnson has been enrolled in your Math 101 class.
              </p>
              <p className="mt-1 text-xs text-blue-600 dark:text-blue-400">
                2 hours ago
              </p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                Assignment submitted
              </h3>
              <p className="mt-1 text-sm text-green-700 dark:text-green-300">
                15 students have submitted their homework for Chapter 5.
              </p>
              <p className="mt-1 text-xs text-green-600 dark:text-green-400">
                4 hours ago
              </p>
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Schedule update
              </h3>
              <p className="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                Your Tuesday 3rd period class has been moved to Room 205.
              </p>
              <p className="mt-1 text-xs text-yellow-600 dark:text-yellow-400">
                Yesterday
              </p>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <div className="w-2 h-2 bg-gray-400 rounded-full mt-2"></div>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-gray-800 dark:text-gray-200">
                System maintenance
              </h3>
              <p className="mt-1 text-sm text-gray-700 dark:text-gray-300">
                The gradebook system will be offline for maintenance this weekend.
              </p>
              <p className="mt-1 text-xs text-gray-600 dark:text-gray-400">
                2 days ago
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeacherCalendarPage;
