{"name": "basic-example", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "blade", "build": "blade build", "serve": "blade serve"}, "author": "ronin", "dependencies": {"@base-ui-components/react": "^1.0.0-beta.1", "@better-auth/sso": "^1.3.0-beta.1", "@ncdai/react-wheel-picker": "^1.0.14", "@react-email/components": "^0.1.1", "@react-three/fiber": "^9.2.0", "@squircle/core": "^1.0.6", "@squircle/paint-polyfill": "^1.0.6", "@squircle/tailwindcss": "^1.0.6", "@suyalcinkaya/gauge": "^0.1.0", "@types/createjs": "^0.0.32", "@types/google.maps": "^3.58.1", "@types/luxon": "^3.6.2", "@types/node": "^24.0.10", "@types/three": "^0.178.1", "better-auth": "^1.2.12", "blade": "3.7.9", "blade-better-auth": "3.7.9", "blade-hono": "3.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "hono": "^4.8.3", "input-otp": "^1.4.2", "lucide-react": "^0.525.0", "luxon": "^3.7.1", "motion": "^12.23.6", "react": "0.0.0-experimental-df12d7eac-20230510", "react-attention": "^1.0.7", "react-dom": "0.0.0-experimental-df12d7eac-20230510", "react-hotkeys-hook": "^5.1.0", "react-icons": "^5.5.0", "resend": "^4.6.0", "ronin": "^6.8.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "three": "^0.178.0", "tw-animate-css": "^1.3.4", "zustand": "^5.0.6"}, "devDependencies": {"@types/react": "19.1.4", "@types/react-dom": "19.1.5", "typescript": "^5.8.3"}}