﻿// triggers/waitlist.ts
import type { AddTrigger, SetTrigger, RemoveTrigger } from 'blade/types';
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

 

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processWaitlistData = (waitlistData: any) => {
      console.log('Waitlist during.add trigger - processing data:', waitlistData);

      // Set default values
      waitlistData.isApproved = waitlistData.isApproved || false;
      waitlistData.createdAt = waitlistData.createdAt || new Date();

      console.log('Waitlist during.add trigger - processed data:', waitlistData);
      return waitlistData;
    };

    // Handle array of waitlist entries
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processWaitlistData);
    } else {
      // Handle single waitlist entry
      typedQuery.with = processWaitlistData(typedQuery.with);
    }

    return typedQuery;
};
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // Set approval timestamp if being approved
    if (typedQuery.to.isApproved === true && !typedQuery.to.approvedAt) {
      typedQuery.to.approvedAt = new Date();
    }

    console.log('Waitlist during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
};
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;



    // Add any validation or cleanup logic for deletions
    console.log('Waitlist during.remove trigger called with query:', typedQuery);
    return typedQuery;
};
