// pages/teacher/[slug]/students-optimized.tsx
import { use, useBatch } from 'blade/server/hooks';
import { useParams } from 'blade/hooks';
import TeacherStudentsPageWithData from '../../../components/teacher/TeacherStudentsPageWithData.client';

// Define types to fix implicit 'any' errors
interface User {
  id: string;
  name: string;
  email: string;
  slug: string;
  role: string;
  teacherId?: string;
  username?: string;
  classId?: string;
  grade?: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface GradeLevel {
  id: string;
  name: string;
  code?: string;
  description?: string;
  category: string;
  educationType: string;
  teacherId: string;
  sortOrder?: number;
  isActive?: boolean;
}

interface ClassItem {
  id: string;
  name: string;
  description?: string;
  teacherId: string;
  schoolId?: string;
  subjectId?: string;
  gradeLevel?: string;
  maxCapacity?: number;
  currentEnrollment?: number;
  isActive?: boolean;
  startDate?: string;
  endDate?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface StudentTeacher {
  id: string;
  studentId: string;
  teacherId: string;
  assignedAt: string;
  status: string;
}

export default function OptimizedStudentsPage() {
  const { slug } = useParams();

  // OPTIMIZED: Single batched query instead of multiple separate queries
  // This prevents the excessive revalidation you're experiencing
  const [allUsers, allGradeLevels, allClasses, allStudentTeachers, allTeachers, allStudents] = useBatch(() => [
    // Get ALL users (teachers and students) in one query
    use.users({
      where: {
        OR: [
          { role: 'teacher' },
          { role: 'student' }
        ]
      }
    }),

    // Get ALL grade levels
    use.gradeLevels({
      where: { isActive: true },
      orderedBy: { ascending: ['sortOrder', 'name'] }
    }),

    // Get ALL classes
    use.classes({
      where: { isActive: true }
    }),

    // Get ALL student-teacher relationships
    use.studentTeachers(),

    // Get ALL teacher records (needed for matching StudentTeacher relationships)
    use.teachers(),

    // Get ALL student records (needed for matching StudentTeacher relationships)
    use.students()
  ]);

  // Find the current teacher user
  const teacher = allUsers.find((u: User) => u.slug === slug && u.role === 'teacher');

  if (!teacher) {
    return null; // Or a loading/error state
  }

  // Find the Teacher record for this user (needed for StudentTeacher relationships)
  const teacherRecord = allTeachers.find((t: any) => t.userId === teacher.id);

  if (!teacherRecord) {
    console.error('Teacher record not found for user:', teacher.id);
    return null;
  }

  // Get active and inactive student relationships for this teacher using Teacher record ID
  const activeRelationships = allStudentTeachers.filter((st: StudentTeacher) =>
    st.teacherId === teacherRecord.id && st.status === 'active'
  );
  const inactiveRelationships = allStudentTeachers.filter((st: StudentTeacher) =>
    st.teacherId === teacherRecord.id && st.status === 'inactive'
  );

  // Get student user data for the relationships
  // Need to map from Student record ID to User ID via Student records
  const activeStudents = activeRelationships
    .map((st: StudentTeacher) => {
      // Find the Student record by ID
      const studentRecord = allStudents.find((s: any) => s.id === st.studentId);
      if (!studentRecord) return null;

      // Find the User record by the Student's userId
      return allUsers.find((u: User) => u.id === studentRecord.userId && u.role === 'student');
    })
    .filter((user: User | null | undefined): user is User => !!user)
    .sort((a: User, b: User) => (a.name || '').localeCompare(b.name || ''));

  const inactiveStudents = inactiveRelationships
    .map((st: StudentTeacher) => {
      // Find the Student record by ID
      const studentRecord = allStudents.find((s: any) => s.id === st.studentId);
      if (!studentRecord) return null;

      // Find the User record by the Student's userId
      return allUsers.find((u: User) => u.id === studentRecord.userId && u.role === 'student');
    })
    .filter((user: User | null | undefined): user is User => !!user)
    .sort((a: User, b: User) => (a.name || '').localeCompare(b.name || ''));

  // Filter grade levels for this teacher
  const teacherGradeLevels = allGradeLevels.filter((grade: GradeLevel) => 
    grade.teacherId === teacher.id && grade.isActive !== false
  );

  // Filter classes for this teacher
  const teacherClasses = allClasses.filter((classItem: ClassItem) => 
    classItem.teacherId === teacher.id && classItem.isActive !== false
  );

  return (
    <TeacherStudentsPageWithData
      teacher={teacher}
      activeStudents={activeStudents}
      inactiveStudents={inactiveStudents}
      teacherGradeLevels={teacherGradeLevels}
      teacherClasses={teacherClasses}
    />
  );
}