#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const STUDENTS_DIR = 'pages/teacher/[slug]';
const SIDEBAR_DIR = 'components/auth/teacher/dual-sidebar';

const versions = {
  students: {
    original: 'students.tsx',
    optimized: 'students-optimized.tsx',
    ultraOptimized: 'students-ultra-optimized.tsx',
    final: 'students-final.tsx',
    minimal: 'students-minimal.tsx',
    backup: 'students-backup.tsx'
  },
  sidebar: {
    original: 'enhanced-sidebar.client.tsx',
    optimized: 'enhanced-sidebar-optimized.client.tsx',
    backup: 'enhanced-sidebar-backup.client.tsx'
  }
};

function backupFile(filePath) {
  const backupPath = filePath.replace('.tsx', '-backup.tsx');
  if (fs.existsSync(filePath) && !fs.existsSync(backupPath)) {
    fs.copyFileSync(filePath, backupPath);
    console.log(`✅ Backed up: ${filePath} -> ${backupPath}`);
  }
}

function switchVersion(component, version) {
  const config = versions[component];
  if (!config || !config[version]) {
    console.error(`❌ Invalid component or version: ${component}.${version}`);
    return;
  }

  const dir = component === 'students' ? STUDENTS_DIR : SIDEBAR_DIR;
  const originalPath = path.join(dir, config.original);
  const versionPath = path.join(dir, config[version]);

  if (!fs.existsSync(versionPath)) {
    console.error(`❌ Version file not found: ${versionPath}`);
    return;
  }

  // Backup original if not already backed up
  backupFile(originalPath);

  // Copy version to original
  fs.copyFileSync(versionPath, originalPath);
  console.log(`✅ Switched ${component} to ${version}: ${versionPath} -> ${originalPath}`);
}

function restoreBackup(component) {
  const config = versions[component];
  const dir = component === 'students' ? STUDENTS_DIR : SIDEBAR_DIR;
  const originalPath = path.join(dir, config.original);
  const backupPath = path.join(dir, config.backup);

  if (!fs.existsSync(backupPath)) {
    console.error(`❌ Backup not found: ${backupPath}`);
    return;
  }

  fs.copyFileSync(backupPath, originalPath);
  console.log(`✅ Restored ${component} from backup: ${backupPath} -> ${originalPath}`);
}

function showUsage() {
  console.log(`
🔧 Revalidation Testing Script

Usage:
  node test-revalidation.js <command> [args]

Commands:
  switch <component> <version>  - Switch to a specific version
  restore <component>           - Restore from backup
  list                         - List available versions
  help                         - Show this help

Components:
  students                     - Student management page
  sidebar                      - Enhanced sidebar component

Student Versions:
  original                     - Current version (students.tsx)
  optimized                    - First optimization attempt
  ultraOptimized              - Ultra-optimized version
  final                       - Final optimized version
  minimal                     - Minimal version for debugging

Sidebar Versions:
  original                     - Current version
  optimized                   - Optimized version with reduced logging

Examples:
  node test-revalidation.js switch students minimal
  node test-revalidation.js switch sidebar optimized
  node test-revalidation.js restore students
  node test-revalidation.js list
`);
}

function listVersions() {
  console.log('\n📋 Available Versions:\n');
  
  Object.entries(versions).forEach(([component, config]) => {
    console.log(`${component.toUpperCase()}:`);
    Object.keys(config).forEach(version => {
      const dir = component === 'students' ? STUDENTS_DIR : SIDEBAR_DIR;
      const filePath = path.join(dir, config[version]);
      const exists = fs.existsSync(filePath);
      const status = exists ? '✅' : '❌';
      console.log(`  ${status} ${version.padEnd(15)} - ${config[version]}`);
    });
    console.log('');
  });
}

// Main execution
const [,, command, ...args] = process.argv;

switch (command) {
  case 'switch':
    const [component, version] = args;
    if (!component || !version) {
      console.error('❌ Usage: switch <component> <version>');
      process.exit(1);
    }
    switchVersion(component, version);
    break;

  case 'restore':
    const [restoreComponent] = args;
    if (!restoreComponent) {
      console.error('❌ Usage: restore <component>');
      process.exit(1);
    }
    restoreBackup(restoreComponent);
    break;

  case 'list':
    listVersions();
    break;

  case 'help':
  default:
    showUsage();
    break;
}