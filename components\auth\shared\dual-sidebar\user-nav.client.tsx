'use client';

import React, { useCallback, useContext } from 'react';
import { cn } from '../../../../lib/utils';
import { Avatar, AvatarImage, AvatarFallback } from '../../../ui/avatar.client';
import { Image } from 'blade/client/components'; // Import RONIN Image component from blade
import { useUnifiedSession } from '../../../../lib/auth-client';
import { useUserInitials } from '../../../../hooks/useAvatarUpdate';
import { getImageUrl } from '../../../../lib/utils/image';
import { ChevronDown } from 'lucide-react';

interface UserNavProps {
  className?: string;
  user?: any;
  onUserNavClick?: () => void; // New prop to handle click from parent
}

// Create a context to pass the flyout state from the toolbar
export const UserNavContext = React.createContext<{
  isUserNavActive: boolean;
  onUserNavClick: () => void;
} | null>(null);

export function UserNav({ className, user: propUser, onUserNavClick }: UserNavProps) {
  const { session } = useUnifiedSession();
  const user = propUser || session?.user;
  const userInitials = useUserInitials(user?.name);
  
  // Get the user image - could be a RONIN Blob object or a string URL
  const currentImage = user?.image;
  const displayImageUrl = getImageUrl(currentImage);
  
  // Try to get context from toolbar, fallback to prop
  const context = useContext(UserNavContext);
  const isUserNavActive = context?.isUserNavActive || false;
  const handleUserNavClick = context?.onUserNavClick || onUserNavClick || (() => {});

  const handleUserClick = useCallback((e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('🎯 UserNav clicked, calling handleUserNavClick');
    handleUserNavClick();
  }, [handleUserNavClick]);

  if (!user) {
    return null;
  }

  return (
    <div className={cn("flex items-center gap-1", className)}>
      {/* Main User Button */}
      <div 
        className={cn(
          "flex py-1 items-center gap-2 rounded-full px-1.5 transition-colors select-none relative cursor-pointer",
          "hover:bg-black/5 dark:hover:bg-white/5",
          isUserNavActive && "bg-black/10 dark:bg-white/10"
        )}
        role="button"
        tabIndex={0}
        aria-label={`${isUserNavActive ? 'Close' : 'Open'} user menu`}
        onClick={handleUserClick}
        style={{ WebkitTapHighlightColor: 'transparent' }}
      >
        <Avatar className="h-7 w-7 ring-1 ring-black/20 dark:ring-white/20 overflow-hidden">
          {currentImage && typeof currentImage === 'object' ? (
            <Image
              src={currentImage}
              alt={user?.name || 'User Avatar'}
              width={28}
              height={28}
              className="h-full w-full object-cover rounded-full"
            />
          ) : displayImageUrl ? (
            <AvatarImage
              src={displayImageUrl}
              alt={user?.name || 'User Avatar'}
              className="object-cover"
            />
          ) : null}
          <AvatarFallback className="bg-gradient-to-br from-blue-400 to-purple-500 text-white font-semibold">
            {userInitials}
          </AvatarFallback>
        </Avatar>
       
      </div>
    </div>
  );
}