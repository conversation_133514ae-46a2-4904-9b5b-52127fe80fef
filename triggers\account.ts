// triggers/account.ts
import type { AddTrigger, SetTrigger, RemoveTrigger } from 'blade/types';

export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processAccountData = (accountData: any) => {
    console.log('Account add trigger - processing data:', accountData);

    // Set default values if needed
    accountData.attempts = accountData.attempts || 0;

      console.log('Account during.add trigger - processed data:', accountData);
      return accountData;
    };

    // Handle array of accounts
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processAccountData);
    } else {
      // Handle single account
      typedQuery.with = processAccountData(typedQuery.with);
    }

    return typedQuery;
};

export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  console.log('Account set trigger - processed data:', typedQuery.to);
  return typedQuery;
};

export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('Account remove trigger called with query:', typedQuery);
  return typedQuery;
};


