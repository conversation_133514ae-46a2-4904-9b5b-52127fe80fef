'use client';

import { useState, useCallback, useEffect } from 'react';
import { Dialog } from '@base-ui-components/react/dialog';
import { X, Users, School, UserCheck, AtSign, Loader2 } from 'lucide-react';
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionPanel,
} from '../../../animate-ui/base/accordion';
import { cn } from '../../../../lib/utils';
import { motion, AnimatePresence } from 'motion/react';

interface Student {
  id: string;
  name: string;
  email: string;
  username?: string;
  grade?: string;
  isActive?: boolean;
}

interface ClassItem {
  id: string;
  name: string;
  description?: string;
  teacherId: string;
  maxCapacity?: number;
  currentEnrollment?: number;
  isActive?: boolean;
}

interface StudentClassAssignment {
  id: string;
  studentId: string;
  classId: string;
  enrolledAt: string;
  status: string;
}

interface ClassAssignmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  classes: ClassItem[];
  students: Student[];
  studentClassAssignments: StudentClassAssignment[];
  teacherId: string;
}

export default function ClassAssignmentDialog({
  isOpen,
  onClose,
  classes,
  students,
  studentClassAssignments,
  teacherId
}: ClassAssignmentDialogProps) {
  const [selectedClassId, setSelectedClassId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [localAssignments, setLocalAssignments] = useState<StudentClassAssignment[]>(studentClassAssignments);
  const [userToStudentIdMap, setUserToStudentIdMap] = useState<Record<string, string>>({});

  // Update local assignments when props change
  useEffect(() => {
    setLocalAssignments(studentClassAssignments);
  }, [studentClassAssignments]);

  // Build User ID to Student ID mapping by calling API
  useEffect(() => {
    const buildUserToStudentMapping = async () => {
      const mapping: Record<string, string> = {};

      for (const student of students) {
        try {
          const response = await fetch('/api/get-student-record', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ userId: student.id }),
          });

          if (response.ok) {
            const result = await response.json();
            if (result.success && result.studentRecord) {
              mapping[student.id] = result.studentRecord.id;
            }
          }
        } catch (error) {
          console.error('Error getting student record for user:', student.id, error);
        }
      }

      setUserToStudentIdMap(mapping);
    };

    if (students.length > 0) {
      buildUserToStudentMapping();
    }
  }, [students]);

  // Get students assigned to the selected class
  const getStudentsInClass = useCallback((classId: string) => {
    return students.filter(student =>
      localAssignments.some(assignment =>
        assignment.studentId === student.id &&
        assignment.classId === classId &&
        assignment.status === 'active'
      )
    );
  }, [students, localAssignments]);

  // Check if a student is in a specific class
  const isStudentInClass = useCallback((userId: string, classId: string) => {
    // Convert User ID to Student record ID
    const studentRecordId = userToStudentIdMap[userId];
    if (!studentRecordId) {
      return false; // If we don't have the mapping yet, assume not in class
    }

    return localAssignments.some(assignment =>
      assignment.studentId === studentRecordId &&
      assignment.classId === classId &&
      assignment.status === 'active'
    );
  }, [localAssignments, userToStudentIdMap]);

  // Toggle student in class with optimistic updates
  const handleToggleStudentInClass = useCallback(async (userId: string, classId: string) => {
    const isCurrentlyInClass = isStudentInClass(userId, classId);
    const studentRecordId = userToStudentIdMap[userId];

    if (!studentRecordId) {
      console.error('❌ No Student record ID found for User ID:', userId);
      return;
    }

    setIsLoading(true);

    try {
      if (isCurrentlyInClass) {
        // Remove student from class
        const assignmentToRemove = localAssignments.find(assignment =>
          assignment.studentId === studentRecordId &&
          assignment.classId === classId &&
          assignment.status === 'active'
        );

        if (assignmentToRemove) {
          // Optimistic update
          setLocalAssignments(prev => prev.filter(a => a.id !== assignmentToRemove.id));

          // Remove from database using API endpoint
          const response = await fetch('/api/assign-student-to-class', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              studentId: userId, // Pass User ID, API will convert to Student record ID
              classId,
              action: 'remove'
            }),
          });

          if (!response.ok) {
            throw new Error('Failed to remove student from class');
          }

          console.log('✅ Student removed from class:', { userId, classId });

          // Dispatch event to notify other components
          window.dispatchEvent(new CustomEvent('studentClassAssignmentChanged', {
            detail: { studentId: userId, classId, action: 'remove' }
          }));
        }
      } else {
        // Add student to class
        const tempId = `temp-${Date.now()}`;
        const newAssignment: StudentClassAssignment = {
          id: tempId,
          studentId: studentRecordId, // Use Student record ID for optimistic update
          classId,
          enrolledAt: new Date().toISOString(),
          status: 'active'
        };

        // Optimistic update
        setLocalAssignments(prev => [...prev, newAssignment]);

        // Add to database using API endpoint
        const response = await fetch('/api/assign-student-to-class', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            studentId: userId, // Pass User ID, API will convert to Student record ID
            classId,
            action: 'add'
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to add student to class');
        }

        const result = await response.json();

        // Replace optimistic update with real data
        setLocalAssignments(prev =>
          prev.map(a => a.id === tempId ? { ...result.assignment, id: result.assignment.id } : a)
        );

        console.log('✅ Student added to class:', { userId, classId, result });

        // Dispatch event to notify other components
        window.dispatchEvent(new CustomEvent('studentClassAssignmentChanged', {
          detail: { studentId: userId, classId, action: 'add', assignment: result.assignment }
        }));
      }
    } catch (error) {
      console.error('❌ Error toggling student in class:', error);

      // Revert optimistic update on error
      setLocalAssignments(studentClassAssignments);

      // You could show a toast notification here
      alert('Failed to update student assignment. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [isStudentInClass, localAssignments, studentClassAssignments, userToStudentIdMap]);

  return (
    <Dialog.Root open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <Dialog.Portal>
        <Dialog.Backdrop className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40" />
        <Dialog.Popup className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-4xl max-h-[90vh] bg-white dark:bg-gray-900 rounded-xl shadow-2xl z-50 overflow-hidden">
        <div className="flex flex-col h-full max-h-[90vh]">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Assign Students to Classes
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Select a class and assign students to it
              </p>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <div className="flex flex-1 overflow-hidden">
            {/* Classes List */}
            <div className="w-1/3 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
              <div className="p-4">
                <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  Your Classes ({classes.length})
                </h3>
                <div className="space-y-2">
                  {classes.map((classItem) => {
                    const studentsInClass = getStudentsInClass(classItem.id);
                    const isSelected = selectedClassId === classItem.id;
                    
                    return (
                      <button
                        key={classItem.id}
                        onClick={() => setSelectedClassId(classItem.id)}
                        className={cn(
                          "w-full p-3 rounded-lg text-left transition-all duration-200",
                          isSelected
                            ? "bg-blue-50 dark:bg-blue-950/30 border-2 border-blue-200 dark:border-blue-700"
                            : "bg-gray-50 dark:bg-gray-800 border-2 border-transparent hover:bg-gray-100 dark:hover:bg-gray-700"
                        )}
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center flex-shrink-0">
                            <School className="w-4 h-4 text-white" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-sm text-gray-900 dark:text-white truncate">
                              {classItem.name}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {studentsInClass.length} students
                            </div>
                          </div>
                        </div>
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Students Assignment */}
            <div className="flex-1 overflow-y-auto">
              {selectedClassId ? (
                <ClassStudentAssignment
                  classItem={classes.find(c => c.id === selectedClassId)!}
                  students={students}
                  isStudentInClass={(studentId) => isStudentInClass(studentId, selectedClassId)}
                  onToggleStudent={(studentId) => handleToggleStudentInClass(studentId, selectedClassId)}
                  isLoading={isLoading}
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <School className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                      Select a Class
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Choose a class from the left to assign students to it
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        </Dialog.Popup>
      </Dialog.Portal>
    </Dialog.Root>
  );
}

// Component for assigning students to a specific class
function ClassStudentAssignment({
  classItem,
  students,
  isStudentInClass,
  onToggleStudent,
  isLoading
}: {
  classItem: ClassItem;
  students: Student[];
  isStudentInClass: (studentId: string) => boolean;
  onToggleStudent: (studentId: string) => Promise<void>;
  isLoading: boolean;
}) {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          {classItem.name}
        </h3>
        {classItem.description && (
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {classItem.description}
          </p>
        )}
        <div className="flex items-center gap-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
          <span>Capacity: {classItem.currentEnrollment || 0}/{classItem.maxCapacity || 30}</span>
          <span>Status: {classItem.isActive ? 'Active' : 'Inactive'}</span>
        </div>
      </div>

      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
          Students ({students.length})
        </h4>
        
        {students.length === 0 ? (
          <div className="text-center py-8">
            <Users className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500 dark:text-gray-400">No students available</p>
          </div>
        ) : (
          <div className="border border-black/10 dark:border-white/10 rounded-xl overflow-hidden shadow-sm">
            <Accordion className="w-full" defaultValue={students.map(s => s.id)}>
              {students.map((student) => {
                const isInClass = isStudentInClass(student.id);
                
                return (
                  <AccordionItem 
                    key={student.id} 
                    value={student.id} 
                    className="border-b border-black/10 dark:border-white/10 last:border-b-0"
                  >
                    <AccordionTrigger className="flex items-center justify-between w-full p-3 text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200">
                      <div className="flex items-center gap-3 flex-1">
                        {/* Student info */}
                        <div className="flex-1 min-w-0 font-manrope_1">
                          <div className="font-medium text-sm text-black/90 dark:text-white/90">
                            {student.name}
                          </div>
                          <div className="text-xs text-black/60 dark:text-white/60 truncate">
                            {student.email}
                          </div>
                          {student.grade && (
                            <div className="text-xs text-blue-600 dark:text-blue-400">
                              Grade {student.grade}
                            </div>
                          )}
                        </div>

                        {/* Login type icon */}
                        <div className="flex-shrink-0">
                          {student.username ? (
                            <UserCheck className="w-4 h-4 text-black/90 dark:text-white/90" />
                          ) : (
                            <AtSign className="w-4 h-4 text-black/90 dark:text-white/90" />
                          )}
                        </div>

                        {/* Assignment status */}
                        <div className="flex-shrink-0">
                          <span className={cn(
                            "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",
                            isInClass
                              ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-700"
                              : "bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-gray-700"
                          )}>
                            {isInClass ? 'Assigned' : 'Not Assigned'}
                          </span>
                        </div>
                      </div>
                    </AccordionTrigger>

                    <AccordionPanel className="px-3 pb-3">
                      <div className="flex justify-end pt-2">
                        {/* Custom Checkbox with Animation */}
                        <div className="flex-shrink-0 flex items-center justify-center">
                          <label
                            className="checkbox-custom block p-3 cursor-pointer relative"
                            style={{
                              '--checkbox-active': '#6E7BF2',
                              '--active-tick': '#ffffff',
                              '--checkbox-lines-offset': isInClass ? '4.5px' : '13.5px',
                              '--background': isInClass ? 'var(--checkbox-active)' : 'none',
                              '--border': isInClass ? 'var(--checkbox-active)' : 'var(--border-default)',
                              '--checkbox-tick-offset': isInClass ? '0px' : '20px',
                              '--checkbox-tick-duration': isInClass ? '.2s' : '.15s',
                              '--checkbox-tick-easing': isInClass ? 'cubic-bezier(0, .45, 1, .5)' : 'ease',
                            } as React.CSSProperties}
                          >
                            <input
                              type="checkbox"
                              checked={isInClass}
                              disabled={isLoading}
                              onChange={async (e) => {
                                if (e.target.checked) {
                                  // Play sound only when checking
                                  const audio = new Audio('https://assets.codepen.io/165585/check.mp3');
                                  audio.play().catch(() => {
                                    // Ignore audio play errors (user interaction required)
                                  });
                                }
                                await onToggleStudent(student.id);
                              }}
                              className="block outline-none border-none bg-none p-0 m-0 w-[18px] h-[18px] opacity-0 absolute"
                              style={{ WebkitAppearance: 'none' } as React.CSSProperties}
                            />

                            {/* Loading spinner overlay */}
                            {isLoading && (
                              <div className="absolute inset-0 flex items-center justify-center">
                                <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
                              </div>
                            )}

                            {/* Main checkbox SVG */}
                            <svg
                              viewBox="0 0 21 18"
                              className="block absolute w-[21px] h-[18px] left-3 top-3 transition-colors duration-200"
                              style={{
                                color: 'var(--checkbox-active)',
                              } as React.CSSProperties}
                            >
                              <symbol id={`tick-path-${student.id}-${classItem.id}`} viewBox="0 0 21 18">
                                <path
                                  d="M5.22003 7.26C5.72003 7.76 7.57 9.7 8.67 11.45C12.2 6.05 15.65 3.5 19.19 1.69"
                                  fill="none"
                                  strokeWidth="2.25"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                              </symbol>
                              <defs>
                                <mask id={`tick-${student.id}-${classItem.id}`}>
                                  <use
                                    className="tick mask"
                                    href={`#tick-path-${student.id}-${classItem.id}`}
                                    stroke="var(--active-tick)"
                                  />
                                </mask>
                              </defs>

                              {/* Checkbox shape */}
                              <path
                                className="shape"
                                d="M1.08722 4.13374C1.29101 2.53185 2.53185 1.29101 4.13374 1.08722C5.50224 0.913124 7.25112 0.75 9 0.75C10.7489 0.75 12.4978 0.913124 13.8663 1.08722C15.4681 1.29101 16.709 2.53185 16.9128 4.13374C17.0869 5.50224 17.25 7.25112 17.25 9C17.25 10.7489 17.0869 12.4978 16.9128 13.8663C16.709 15.4681 15.4682 16.709 13.8663 16.9128C12.4978 17.0869 10.7489 17.25 9 17.25C7.25112 17.25 5.50224 17.0869 4.13374 16.9128C2.53185 16.709 1.29101 15.4681 1.08722 13.8663C0.913124 12.4978 0.75 10.7489 0.75 9C0.75 7.25112 0.913124 5.50224 1.08722 4.13374Z"
                                strokeWidth="1.5px"
                                stroke="var(--border)"
                                fill="var(--background)"
                                style={{
                                  transition: 'fill .25s linear, stroke .25s linear'
                                } as React.CSSProperties}
                              />

                              {/* Tick mark */}
                              <use
                                className="tick"
                                href={`#tick-path-${student.id}-${classItem.id}`}
                                stroke="currentColor"
                                strokeDasharray="20"
                                strokeDashoffset="var(--checkbox-tick-offset)"
                                style={{
                                  transition: 'stroke-dashoffset var(--checkbox-tick-duration) var(--checkbox-tick-easing)'
                                } as React.CSSProperties}
                              />

                              {/* Background fill with mask */}
                              <path
                                fill="#2C2C31"
                                mask={`url(#tick-${student.id}-${classItem.id})`}
                                d="M4.03909 0.343217C5.42566 0.166822 7.20841 0 9 0C10.7916 0 12.5743 0.166822 13.9609 0.343217C15.902 0.590152 17.4098 2.09804 17.6568 4.03909C17.8332 5.42566 18 7.20841 18 9C18 10.7916 17.8332 12.5743 17.6568 13.9609C17.4098 15.902 15.902 17.4098 13.9609 17.6568C12.5743 17.8332 10.7916 18 9 18C7.20841 18 5.42566 17.8332 4.03909 17.6568C2.09805 17.4098 0.590152 15.902 0.343217 13.9609C0.166822 12.5743 0 10.7916 0 9C0 7.20841 0.166822 5.42566 0.343217 4.03909C0.590151 2.09805 2.09804 0.590152 4.03909 0.343217Z"
                              />
                            </svg>

                            {/* Lines SVG */}
                            <svg
                              className="lines block absolute w-[11px] h-[11px] top-[9px] right-[2px] fill-none stroke-current stroke-[1.25] pointer-events-none"
                              viewBox="0 0 11 11"
                              style={{
                                strokeLinecap: 'round',
                                strokeDasharray: '4.5px',
                                strokeDashoffset: 'var(--checkbox-lines-offset)',
                                color: 'var(--checkbox-active)',
                                transition: 'stroke-dashoffset 0.2s ease'
                              } as React.CSSProperties}
                            >
                              <path d="M5.88086 5.89441L9.53504 4.26746" />
                              <path d="M5.5274 8.78838L9.45391 9.55161" />
                              <path d="M3.49371 4.22065L5.55387 0.79198" />
                            </svg>
                          </label>
                        </div>
                      </div>
                    </AccordionPanel>
                  </AccordionItem>
                );
              })}
            </Accordion>
          </div>
        )}
      </div>
    </div>
  );
}
