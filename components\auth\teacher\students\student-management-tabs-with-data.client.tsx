'use client';

import React, { useState, memo, useEffect } from 'react';
import { useMutation } from 'blade/client/hooks';
import {StudentEditForm} from "../students/student-edit-form.client"
import { 
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ist,
  Ta<PERSON>Trigger,
  TabsContent,
  TabsContents,
} from '../../../ui/animate-ui/components/tabs';
import { Avatar, AvatarFallback } from '../../../ui/avatar.client';
import { Image } from 'blade/client/components';
import { useUserInitials } from '../../../../hooks/useAvatarUpdate';
import { Users, UserPlus, UserX, User, Check, AtSign, UserCheck, Trash2 } from 'lucide-react';
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionPanel,
} from '../../../animate-ui/base/accordion';

interface Student {
  id: string;
  name: string;
  email: string;
  username?: string;
  classId?: string;
  grade?: string;
  isActive?: boolean;
  teacherId?: string;
  createdAt?: string;
  image?: any; // StoredObject or string for avatar
  generateUsername?: boolean; // Add this for pending students
}

interface PendingStudent {
  id: string;
  name: string;
  email: string;
  generateUsername: boolean;
}

interface Teacher {
  id: string;
  name: string;
  email: string;
  slug: string;
}

interface GradeLevel {
  id: string;
  name: string;
  code?: string;
  description?: string;
  category: string;
  educationType: string;
  teacherId: string;
  sortOrder?: number;
  isActive?: boolean;
}

interface ClassItem {
  id: string;
  name: string;
  description?: string;
  teacherId: string;
  schoolId?: string;
  subjectId?: string;
  gradeLevel?: string;
  maxCapacity?: number;
  currentEnrollment?: number;
  isActive?: boolean;
  startDate?: string;
  endDate?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface StudentManagementTabsWithDataProps {
  students: Student[];
  removedStudents?: Student[];
  teacher: Teacher;
  availableGradeLevels?: GradeLevel[];
  teacherClasses?: ClassItem[];
}

// Helper function interfaces
interface CurrentStudentsTabProps {
  students: Student[];
  onEditStudent: (student: Student) => void;
  onRemoveStudent: (studentId: string) => void;
  onToggleStatus: (student: Student) => void;
  teacherClasses?: ClassItem[];
}

interface RemovedStudentsTabProps {
  removedStudents: Student[];
  onRestoreStudent: (studentId: string) => void;
}

// Current Students Tab Component with Accordion Layout
function CurrentStudentsTab({
  students,
  onEditStudent,
  onRemoveStudent,
  onToggleStatus,
  teacherClasses = [],
  pendingStudents = [],
  existingStudents = [],
  studentClassAssignments = {},
  existingStudentClassAssignments = {},
  removeStudent,
  removeExistingStudent,
  teacher
}: CurrentStudentsTabProps & {
  pendingStudents?: PendingStudent[];
  existingStudents?: Student[];
  studentClassAssignments?: Record<string, string[]>;
  existingStudentClassAssignments?: Record<string, string[]>;
  removeStudent?: (id: string) => void;
  removeExistingStudent?: (id: string) => void;
  teacher?: Teacher;
}) {
  if (students.length === 0 && pendingStudents.length === 0 && existingStudents.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
        <h2 className="text-xl font-semibold mb-4">No Students Yet</h2>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          You haven't added any students yet. Use the command menu (Ctrl+K) or the "Add New Student" tab to get started.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Pending Students Accordion */}
      {pendingStudents.length > 0 && (
        <div className="space-y-3">
          <h3 className="text-xs font-manrope_1 font-medium text-black/80 dark:text-white/80">
            Students to Invite <span className="text-black dark:text-white">({pendingStudents.length})</span>
          </h3>
          <div className="border border-black/10 dark:border-white/10 rounded-xl overflow-hidden shadow-sm">
            <Accordion className="w-full max-h-60 custom-scrollbar overflow-y-auto">
              {pendingStudents.map((student) => {
                const assignedClasses = studentClassAssignments[student.id] || [];
                const classCount = assignedClasses.length;

                return (
                  <AccordionItem key={student.id} value={student.id} className="border-b border-black/10 dark:border-white/10 last:border-b-0 data-[open]:rounded-xl data-[open]:bg-gradient-to-br data-[open]:from-zinc-100 data-[open]:to-zinc-200 data-[open]:text-zinc-800 data-[open]:shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:data-[open]:bg-gradient-to-b dark:data-[open]:from-[#212026] dark:data-[open]:via-[#212026] dark:data-[open]:to-[#29282e] dark:data-[open]:text-zinc-50 dark:data-[open]:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] data-[open]:border-transparent transition-all duration-200">
                    <AccordionTrigger className="flex items-center justify-between w-full p-3 text-left hover:bg-gradient-to-r hover:from-[#f8f8f8] hover:via-[#f0f0f0] hover:to-[#f4f4f4] dark:hover:bg-gradient-to-r dark:hover:from-[#1a1a1c] dark:hover:via-[#1f1f21] dark:hover:to-[#1c1c1e] data-[panel-open]:hover:bg-transparent data-[panel-open]:hover:from-transparent data-[panel-open]:hover:via-transparent data-[panel-open]:hover:to-transparent transition-all duration-200">
                      <div className="flex items-center gap-3 flex-1">
                        {/* Check mark */}
                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-[#45bd73] flex items-center justify-center">
                          <Check className="w-3 h-3 text-black" />
                        </div>

                        {/* Student info */}
                        <div className="flex-1 min-w-0 font-manrope_1">
                          <div className="font-medium text-xs text-black/80 dark:text-white/80">{student.name}</div>
                          <div className="text-[10px] text-black/60 dark:text-white/60 truncate">{student.email}</div>
                        </div>

                        {/* Login type icon */}
                        <div className="flex-shrink-0">
                          {student.generateUsername ? (
                            <UserCheck className="w-4 h-4 text-black/90 dark:text-white/90" />
                          ) : (
                            <AtSign className="w-4 h-4 text-black/90 dark:text-white/90" />
                          )}
                        </div>

                        {/* Class count badge - always shown */}
                        <div className="flex-shrink-0 mr-1">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-manrope_1 border border-black/10 dark:border-white/10 text-black/80 dark:text-white/80 bg-gradient-to-r from-[#e1dfdf] via-[#d8d7d7] to-[#dad8d8] dark:from-[#242427] dark:via-[#232325] dark:to-[#202023]">
                            {classCount} {classCount === 1 ? 'class' : 'classes'}
                          </span>
                        </div>
                      </div>
                    </AccordionTrigger>

                    <AccordionPanel className="px-3 pb-3">
                      <div className="space-y-3 pt-2">
                        {/* Class assignments */}
                        {assignedClasses.length > 0 && (
                          <div className="space-y-2">
                            <div className="text-xs font-medium text-gray-700 dark:text-gray-300">Assigned Classes:</div>
                            <div className="flex flex-wrap gap-1">
                              {assignedClasses.map(classId => {
                                const className = teacherClasses.find(c => c.id === classId)?.name;
                                return (
                                  <span key={classId} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                    {className}
                                  </span>
                                );
                              })}
                            </div>
                          </div>
                        )}

                        {/* Remove button */}
                        <div className="flex justify-end pt-2">
                          <button
                            onClick={() => removeStudent?.(student.id)}
                            className="flex items-center gap-1 px-2 py-1 text-xs text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                          >
                            <Trash2 className="w-3 h-3" />
                            Remove
                          </button>
                        </div>
                      </div>
                    </AccordionPanel>
                  </AccordionItem>
                );
              })}
            </Accordion>
          </div>
        </div>
      )}

      {/* Existing Students Accordion */}
      {existingStudents.length > 0 && (
        <div className="space-y-3">
          <h3 className="text-xs font-manrope_1 font-medium text-black/80 dark:text-white/80">
            Existing Students <span className="text-black dark:text-white">({existingStudents.length})</span>
          </h3>
          <div className="border border-black/10 dark:border-white/10 rounded-xl overflow-hidden shadow-sm">
            <Accordion className="w-full max-h-60 custom-scrollbar overflow-y-auto">
              {existingStudents.map((student) => {
                const assignedClasses = existingStudentClassAssignments[student.id] || [];
                const classCount = assignedClasses.length;

                return (
                  <AccordionItem key={student.id} value={student.id} className="border-b border-black/10 dark:border-white/10 last:border-b-0 data-[open]:rounded-xl data-[open]:bg-gradient-to-br data-[open]:from-zinc-100 data-[open]:to-zinc-200 data-[open]:text-zinc-800 data-[open]:shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:data-[open]:bg-gradient-to-b dark:data-[open]:from-[#212026] dark:data-[open]:via-[#212026] dark:data-[open]:to-[#29282e] dark:data-[open]:text-zinc-50 dark:data-[open]:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] data-[open]:border-transparent transition-all duration-200">
                    <AccordionTrigger className="flex items-center justify-between w-full p-3 text-left hover:bg-gradient-to-r hover:from-[#f8f8f8] hover:via-[#f0f0f0] hover:to-[#f4f4f4] dark:hover:bg-gradient-to-r dark:hover:from-[#1a1a1c] dark:hover:via-[#1f1f21] dark:hover:to-[#1c1c1e] data-[panel-open]:hover:bg-transparent data-[panel-open]:hover:from-transparent data-[panel-open]:hover:via-transparent data-[panel-open]:hover:to-transparent transition-all duration-200">
                      <div className="flex items-center gap-3 flex-1">
                        {/* Existing student icon */}
                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-[#3b82f6] flex items-center justify-center">
                          <UserCheck className="w-3 h-3 text-white" />
                        </div>

                        {/* Student info */}
                        <div className="flex-1 min-w-0 font-manrope_1">
                          <div className="font-medium text-xs text-black/80 dark:text-white/80">{student.name}</div>
                          <div className="text-[10px] text-black/60 dark:text-white/60 truncate">{student.email}</div>
                        </div>

                        {/* Grade badge if available */}
                        {student.grade && (
                          <div className="flex-shrink-0">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-manrope_1 border border-black/10 dark:border-white/10 text-black/80 dark:text-white/80 bg-gradient-to-r from-[#e1dfdf] via-[#d8d7d7] to-[#dad8d8] dark:from-[#242427] dark:via-[#232325] dark:to-[#202023]">
                              Grade {student.grade}
                            </span>
                          </div>
                        )}

                        {/* Class count badge */}
                        <div className="flex-shrink-0 mr-1">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-manrope_1 border border-black/10 dark:border-white/10 text-black/80 dark:text-white/80 bg-gradient-to-r from-[#e1dfdf] via-[#d8d7d7] to-[#dad8d8] dark:from-[#242427] dark:via-[#232325] dark:to-[#202023]">
                            {classCount} {classCount === 1 ? 'class' : 'classes'}
                          </span>
                        </div>
                      </div>
                    </AccordionTrigger>

                    <AccordionPanel className="px-3 pb-3">
                      <div className="space-y-3 pt-2">
                        {/* Class assignments */}
                        {assignedClasses.length > 0 && (
                          <div className="space-y-2">
                            <div className="text-xs font-medium text-gray-700 dark:text-gray-300">Assigned Classes:</div>
                            <div className="flex flex-wrap gap-1">
                              {assignedClasses.map(classId => {
                                const className = teacherClasses.find(c => c.id === classId)?.name;
                                return (
                                  <span key={classId} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                    {className}
                                  </span>
                                );
                              })}
                            </div>
                          </div>
                        )}

                        {/* Remove button */}
                        <div className="flex justify-end pt-2">
                          <button
                            onClick={() => removeExistingStudent?.(student.id)}
                            className="flex items-center gap-1 px-2 py-1 text-xs text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                          >
                            <Trash2 className="w-3 h-3" />
                            Remove
                          </button>
                        </div>
                      </div>
                    </AccordionPanel>
                  </AccordionItem>
                );
              })}
            </Accordion>
          </div>
        </div>
      )}

      {/* All Students Accordion - Combined view */}
      {(students.length > 0 || pendingStudents.length > 0 || existingStudents.length > 0) && (
        <div className="space-y-3">
          <h3 className="text-xs font-manrope_1 font-medium text-black/80 dark:text-white/80">
            All Students <span className="text-black dark:text-white">({students.length + pendingStudents.length + existingStudents.length})</span>
          </h3>
          <div className="border border-black/10 dark:border-white/10 rounded-xl overflow-hidden shadow-sm">
            <Accordion className="w-full max-h-96 custom-scrollbar overflow-y-auto">
              {/* Pending Students (from command menu) */}
              {pendingStudents.map((student) => {
                const assignedClasses = studentClassAssignments[student.id] || [];
                const classCount = assignedClasses.length;

                return (
                  <AccordionItem key={`pending-${student.id}`} value={`pending-${student.id}`} className="border-b border-black/10 dark:border-white/10 last:border-b-0 data-[open]:rounded-xl data-[open]:bg-gradient-to-br data-[open]:from-zinc-100 data-[open]:to-zinc-200 data-[open]:text-zinc-800 data-[open]:shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:data-[open]:bg-gradient-to-b dark:data-[open]:from-[#212026] dark:data-[open]:via-[#212026] dark:data-[open]:to-[#29282e] dark:data-[open]:text-zinc-50 dark:data-[open]:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] data-[open]:border-transparent transition-all duration-200">
                    <AccordionTrigger className="flex items-center justify-between w-full p-3 text-left hover:bg-gradient-to-r hover:from-[#f8f8f8] hover:via-[#f0f0f0] hover:to-[#f4f4f4] dark:hover:bg-gradient-to-r dark:hover:from-[#1a1a1c] dark:hover:via-[#1f1f21] dark:hover:to-[#1c1c1e] data-[panel-open]:hover:bg-transparent data-[panel-open]:hover:from-transparent data-[panel-open]:hover:via-transparent data-[panel-open]:hover:to-transparent transition-all duration-200">
                      <div className="flex items-center gap-3 flex-1">
                        {/* Pending student icon */}
                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-[#45bd73] flex items-center justify-center">
                          <Check className="w-3 h-3 text-black" />
                        </div>

                        {/* Student info */}
                        <div className="flex-1 min-w-0 font-manrope_1">
                          <div className="font-medium text-xs text-black/80 dark:text-white/80">{student.name}</div>
                          <div className="text-[10px] text-black/60 dark:text-white/60 truncate">{student.email}</div>
                          <div className="text-[9px] text-green-600 dark:text-green-400 font-medium">Pending Invitation</div>
                        </div>

                        {/* Login type icon */}
                        <div className="flex-shrink-0">
                          {student.generateUsername ? (
                            <UserCheck className="w-4 h-4 text-black/90 dark:text-white/90" />
                          ) : (
                            <AtSign className="w-4 h-4 text-black/90 dark:text-white/90" />
                          )}
                        </div>

                        {/* Class count badge */}
                        <div className="flex-shrink-0 mr-1">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-manrope_1 border border-black/10 dark:border-white/10 text-black/80 dark:text-white/80 bg-gradient-to-r from-[#e1dfdf] via-[#d8d7d7] to-[#dad8d8] dark:from-[#242427] dark:via-[#232325] dark:to-[#202023]">
                            {classCount} {classCount === 1 ? 'class' : 'classes'}
                          </span>
                        </div>
                      </div>
                    </AccordionTrigger>

                    <AccordionPanel className="px-3 pb-3">
                      <div className="space-y-3 pt-2">
                        {/* Class assignments */}
                        {assignedClasses.length > 0 && (
                          <div className="space-y-2">
                            <div className="text-xs font-medium text-gray-700 dark:text-gray-300">Assigned Classes:</div>
                            <div className="flex flex-wrap gap-1">
                              {assignedClasses.map(classId => {
                                const className = teacherClasses.find(c => c.id === classId)?.name;
                                return (
                                  <span key={classId} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                    {className}
                                  </span>
                                );
                              })}
                            </div>
                          </div>
                        )}

                        {/* Remove button */}
                        <div className="flex justify-end pt-2">
                          <button
                            onClick={() => removeStudent?.(student.id)}
                            className="flex items-center gap-1 px-2 py-1 text-xs text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                          >
                            <Trash2 className="w-3 h-3" />
                            Remove
                          </button>
                        </div>
                      </div>
                    </AccordionPanel>
                  </AccordionItem>
                );
              })}

              {/* Existing Students (found during creation) */}
              {existingStudents.map((student) => {
                const assignedClasses = existingStudentClassAssignments[student.id] || [];
                const classCount = assignedClasses.length;

                return (
                  <AccordionItem key={`existing-${student.id}`} value={`existing-${student.id}`} className="border-b border-black/10 dark:border-white/10 last:border-b-0 data-[open]:rounded-xl data-[open]:bg-gradient-to-br data-[open]:from-blue-50 data-[open]:to-blue-100 data-[open]:text-blue-900 data-[open]:shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(59,130,246,0.3)_inset,0_0.5px_0_1.5px_rgba(59,130,246,0.2)_inset] dark:data-[open]:bg-gradient-to-b dark:data-[open]:from-blue-950/40 dark:data-[open]:via-blue-950/30 dark:data-[open]:to-blue-950/40 dark:data-[open]:text-blue-300 dark:data-[open]:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgba(59,130,246,0.3)_inset,0_0.5px_0_1.5px_rgba(59,130,246,0.2)_inset] data-[open]:border-transparent transition-all duration-200">
                    <AccordionTrigger className="flex items-center justify-between w-full p-3 text-left hover:bg-gradient-to-r hover:from-blue-50 hover:via-blue-50 hover:to-blue-100 dark:hover:bg-gradient-to-r dark:hover:from-blue-950/20 dark:hover:via-blue-950/30 dark:hover:to-blue-950/20 data-[panel-open]:hover:bg-transparent data-[panel-open]:hover:from-transparent data-[panel-open]:hover:via-transparent data-[panel-open]:hover:to-transparent transition-all duration-200">
                      <div className="flex items-center gap-3 flex-1">
                        {/* Existing student icon */}
                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-[#3b82f6] flex items-center justify-center">
                          <UserCheck className="w-3 h-3 text-white" />
                        </div>

                        {/* Student info */}
                        <div className="flex-1 min-w-0 font-manrope_1">
                          <div className="font-medium text-xs text-black/80 dark:text-white/80">{student.name}</div>
                          <div className="text-[10px] text-black/60 dark:text-white/60 truncate">{student.email}</div>
                          <div className="text-[9px] text-blue-600 dark:text-blue-400 font-medium">Added by Another Teacher</div>
                        </div>

                        {/* Grade badge if available */}
                        {student.grade && (
                          <div className="flex-shrink-0">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-manrope_1 border border-blue-200 dark:border-blue-700 text-blue-800 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30">
                              Grade {student.grade}
                            </span>
                          </div>
                        )}

                        {/* Class count badge */}
                        <div className="flex-shrink-0 mr-1">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-manrope_1 border border-blue-200 dark:border-blue-700 text-blue-800 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30">
                            {classCount} {classCount === 1 ? 'class' : 'classes'}
                          </span>
                        </div>
                      </div>
                    </AccordionTrigger>

                    <AccordionPanel className="px-3 pb-3">
                      <div className="space-y-3 pt-2">
                        {/* Class assignments */}
                        {assignedClasses.length > 0 && (
                          <div className="space-y-2">
                            <div className="text-xs font-medium text-blue-700 dark:text-blue-300">Assigned Classes:</div>
                            <div className="flex flex-wrap gap-1">
                              {assignedClasses.map(classId => {
                                const className = teacherClasses.find(c => c.id === classId)?.name;
                                return (
                                  <span key={classId} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-200 text-blue-900 dark:bg-blue-800 dark:text-blue-200">
                                    {className}
                                  </span>
                                );
                              })}
                            </div>
                          </div>
                        )}

                        {/* Remove button */}
                        <div className="flex justify-end pt-2">
                          <button
                            onClick={() => removeExistingStudent?.(student.id)}
                            className="flex items-center gap-1 px-2 py-1 text-xs text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                          >
                            <Trash2 className="w-3 h-3" />
                            Remove
                          </button>
                        </div>
                      </div>
                    </AccordionPanel>
                  </AccordionItem>
                );
              })}

              {/* Database Students (your current students) */}
              {students.map((student) => {
                // TODO: Implement StudentClass relationship lookup when needed
                // For now, show 0 classes since StudentClass relationships aren't implemented yet
                const classCount = 0;

                return (
                  <AccordionItem key={`student-${student.id}`} value={`student-${student.id}`} className="border-b border-black/10 dark:border-white/10 last:border-b-0 data-[open]:rounded-xl data-[open]:bg-gradient-to-br data-[open]:from-zinc-100 data-[open]:to-zinc-200 data-[open]:text-zinc-800 data-[open]:shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:data-[open]:bg-gradient-to-b dark:data-[open]:from-[#212026] dark:data-[open]:via-[#212026] dark:data-[open]:to-[#29282e] dark:data-[open]:text-zinc-50 dark:data-[open]:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] data-[open]:border-transparent transition-all duration-200">
                    <AccordionTrigger className="flex items-center justify-between w-full p-3 text-left hover:bg-gradient-to-r hover:from-[#f8f8f8] hover:via-[#f0f0f0] hover:to-[#f4f4f4] dark:hover:bg-gradient-to-r dark:hover:from-[#1a1a1c] dark:hover:via-[#1f1f21] dark:hover:to-[#1c1c1e] data-[panel-open]:hover:bg-transparent data-[panel-open]:hover:from-transparent data-[panel-open]:hover:via-transparent data-[panel-open]:hover:to-transparent transition-all duration-200">
                      <div className="flex items-center gap-3 flex-1">
                        {/* Student avatar */}
                        <div className="flex-shrink-0">
                          <Avatar className="h-8 w-8 ring-2 ring-black/10 dark:ring-white/10">
                            {student.image && typeof student.image === 'object' ? (
                              <Image
                                src={student.image}
                                alt={student.name || 'Student'}
                                width={32}
                                height={32}
                                className="h-full w-full object-cover"
                              />
                            ) : (
                              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-medium text-xs">
                                {useUserInitials(student.name || student.email || 'Student')}
                              </AvatarFallback>
                            )}
                          </Avatar>
                        </div>

                        {/* Student info */}
                        <div className="flex-1 min-w-0 font-manrope_1">
                          <div className="font-medium text-xs text-black/80 dark:text-white/80">{student.name || 'Unnamed Student'}</div>
                          <div className="text-[10px] text-black/60 dark:text-white/60 truncate">{student.email}</div>
                          <div className="text-[9px] text-gray-600 dark:text-gray-400 font-medium">
                            {student.isActive !== false ? 'Active Student' : 'Inactive Student'}
                          </div>
                        </div>

                        {/* Login type icon */}
                        <div className="flex-shrink-0">
                          {student.username ? (
                            <UserCheck className="w-4 h-4 text-black/90 dark:text-white/90" />
                          ) : (
                            <AtSign className="w-4 h-4 text-black/90 dark:text-white/90" />
                          )}
                        </div>

                        {/* Grade badge if available */}
                        {student.grade && (
                          <div className="flex-shrink-0">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-manrope_1 border border-black/10 dark:border-white/10 text-black/80 dark:text-white/80 bg-gradient-to-r from-[#e1dfdf] via-[#d8d7d7] to-[#dad8d8] dark:from-[#242427] dark:via-[#232325] dark:to-[#202023]">
                              Grade {student.grade}
                            </span>
                          </div>
                        )}

                        {/* Class count badge */}
                        <div className="flex-shrink-0 mr-1">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-manrope_1 border border-black/10 dark:border-white/10 text-black/80 dark:text-white/80 bg-gradient-to-r from-[#e1dfdf] via-[#d8d7d7] to-[#dad8d8] dark:from-[#242427] dark:via-[#232325] dark:to-[#202023]">
                            {classCount} {classCount === 1 ? 'class' : 'classes'}
                          </span>
                        </div>
                      </div>
                    </AccordionTrigger>

                    <AccordionPanel className="px-3 pb-3">
                      <div className="space-y-3 pt-2">
                        {/* Student actions */}
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => onToggleStatus(student)}
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                student.isActive !== false
                                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                              }`}
                            >
                              {student.isActive !== false ? 'Active' : 'Inactive'}
                            </button>
                          </div>

                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => onEditStudent(student)}
                              className="flex items-center gap-1 px-2 py-1 text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded"
                            >
                              Edit
                            </button>
                            <button
                              onClick={() => onRemoveStudent(student.id)}
                              className="flex items-center gap-1 px-2 py-1 text-xs text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                            >
                              <Trash2 className="w-3 h-3" />
                              Remove
                            </button>
                          </div>
                        </div>
                      </div>
                    </AccordionPanel>
                  </AccordionItem>
                );
              })}
            </Accordion>
          </div>
        </div>
      )}
    </div>
  );
}

// Add Student Tab Component
function AddStudentTab({ teacherClasses = [] }: { teacherClasses?: ClassItem[] }) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  return (
    <div className="space-y-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Add New Student</h2>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Click the button below to open the student management dialog and add new students to your class.
        </p>
        <div className="space-y-4">
          {/* Direct button that opens the dialog - only used in this tab */}
          <button
            onClick={() => setIsDialogOpen(true)}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 px-4 py-2"
          >
            <UserPlus className="h-4 w-4" />
            Add New Student
          </button>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
              <h3 className="font-medium mb-2">Bulk Import</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Import multiple students from a CSV file
              </p>
              <button className="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400">
                Coming Soon
              </button>
            </div>
            <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
              <h3 className="font-medium mb-2">Class Enrollment</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Enroll students in existing classes
              </p>
              <button className="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400">
                Manage Classes
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Student Management Dialog - only rendered when this tab is active and dialog is open */}
      {/* <StudentManagementDialog
        isOpen={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        teacherClasses={teacherClasses}
      /> */}
    </div>
  );
}

// Removed Students Tab Component
function RemovedStudentsTab({
  removedStudents,
  onRestoreStudent
}: RemovedStudentsTabProps) {
  if (removedStudents.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
        <h2 className="text-xl font-semibold mb-4">No Removed Students</h2>
        <p className="text-gray-600 dark:text-gray-400">
          Students you remove from your class will appear here. You can restore them at any time.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <div className="space-y-3">
        <h3 className="text-xs font-manrope_1 font-medium text-black/80 dark:text-white/80">
          Removed Students <span className="text-black dark:text-white">({removedStudents.length})</span>
        </h3>
        <div className="border border-black/10 dark:border-white/10 rounded-xl overflow-hidden shadow-sm">
          <Accordion className="w-full max-h-96 custom-scrollbar overflow-y-auto">
            {removedStudents.map((student) => {
              return (
                <AccordionItem key={`removed-${student.id}`} value={`removed-${student.id}`} className="border-b border-black/10 dark:border-white/10 last:border-b-0 data-[open]:rounded-xl data-[open]:bg-gradient-to-br data-[open]:from-red-50 data-[open]:to-red-100 data-[open]:text-red-900 data-[open]:shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(239,68,68,0.3)_inset,0_0.5px_0_1.5px_rgba(239,68,68,0.2)_inset] dark:data-[open]:bg-gradient-to-b dark:data-[open]:from-red-950/40 dark:data-[open]:via-red-950/30 dark:data-[open]:to-red-950/40 dark:data-[open]:text-red-300 dark:data-[open]:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgba(239,68,68,0.3)_inset,0_0.5px_0_1.5px_rgba(239,68,68,0.2)_inset] data-[open]:border-transparent transition-all duration-200">
                  <AccordionTrigger className="flex items-center justify-between w-full p-3 text-left hover:bg-gradient-to-r hover:from-red-50 hover:via-red-50 hover:to-red-100 dark:hover:bg-gradient-to-r dark:hover:from-red-950/20 dark:hover:via-red-950/30 dark:hover:to-red-950/20 data-[panel-open]:hover:bg-transparent data-[panel-open]:hover:from-transparent data-[panel-open]:hover:via-transparent data-[panel-open]:hover:to-transparent transition-all duration-200">
                    <div className="flex items-center gap-3 flex-1">
                      {/* Removed student avatar */}
                      <div className="flex-shrink-0">
                        <Avatar className="h-8 w-8 ring-2 ring-red-200 dark:ring-red-800">
                          {student.image && typeof student.image === 'object' ? (
                            <Image
                              src={student.image}
                              alt={student.name || 'Student'}
                              width={32}
                              height={32}
                              className="h-full w-full object-cover opacity-60"
                            />
                          ) : (
                            <AvatarFallback className="bg-gradient-to-br from-red-500 to-red-600 text-white font-medium text-xs opacity-60">
                              {useUserInitials(student.name || student.email || 'Student')}
                            </AvatarFallback>
                          )}
                        </Avatar>
                      </div>

                      {/* Student info */}
                      <div className="flex-1 min-w-0 font-manrope_1">
                        <div className="font-medium text-xs text-black/80 dark:text-white/80 line-through opacity-60">{student.name || 'Unnamed Student'}</div>
                        <div className="text-[10px] text-black/60 dark:text-white/60 truncate opacity-60">{student.email}</div>
                        <div className="text-[9px] text-red-600 dark:text-red-400 font-medium">Removed from Class</div>
                      </div>

                      {/* Login type icon */}
                      <div className="flex-shrink-0 opacity-60">
                        {student.username ? (
                          <UserCheck className="w-4 h-4 text-black/90 dark:text-white/90" />
                        ) : (
                          <AtSign className="w-4 h-4 text-black/90 dark:text-white/90" />
                        )}
                      </div>

                      {/* Grade badge if available */}
                      {student.grade && (
                        <div className="flex-shrink-0 opacity-60">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-manrope_1 border border-red-200 dark:border-red-700 text-red-800 dark:text-red-300 bg-red-100 dark:bg-red-900/30">
                            Grade {student.grade}
                          </span>
                        </div>
                      )}
                    </div>
                  </AccordionTrigger>

                  <AccordionPanel className="px-3 pb-3">
                    <div className="space-y-3 pt-2">
                      {/* Restore button */}
                      <div className="flex justify-end">
                        <button
                          onClick={() => onRestoreStudent(student.id)}
                          className="flex items-center gap-1 px-3 py-1.5 text-xs text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-md border border-green-200 dark:border-green-700 font-medium"
                        >
                          <UserPlus className="w-3 h-3" />
                          Restore Student
                        </button>
                      </div>
                    </div>
                  </AccordionPanel>
                </AccordionItem>
              );
            })}
          </Accordion>
        </div>
      </div>
    </div>
  );
}

function StudentManagementTabsWithData({
  students: initialStudents,
  removedStudents: initialRemovedStudents = [],
  teacher,
  availableGradeLevels = [],
  teacherClasses = []
}: StudentManagementTabsWithDataProps) {


  const [activeTab, setActiveTab] = useState<'current' | 'removed'>('current');
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);

  // Listen for class assignment changes to update student badges
  useEffect(() => {
    const handleAssignmentChange = (event: CustomEvent) => {
      const { studentId, classId, action } = event.detail;
      console.log('📢 StudentManagementTabs received assignment change:', { studentId, classId, action });

      // Update local student class assignments state
      // Note: studentId here is the User ID, not Student record ID
      if (action === 'add') {
        setStudentClassAssignments(prev => ({
          ...prev,
          [studentId]: [...(prev[studentId] || []), classId]
        }));
        setExistingStudentClassAssignments(prev => ({
          ...prev,
          [studentId]: [...(prev[studentId] || []), classId]
        }));
      } else if (action === 'remove') {
        setStudentClassAssignments(prev => ({
          ...prev,
          [studentId]: (prev[studentId] || []).filter(id => id !== classId)
        }));
        setExistingStudentClassAssignments(prev => ({
          ...prev,
          [studentId]: (prev[studentId] || []).filter(id => id !== classId)
        }));
      }
    };

    window.addEventListener('studentClassAssignmentChanged', handleAssignmentChange as EventListener);

    return () => {
      window.removeEventListener('studentClassAssignmentChanged', handleAssignmentChange as EventListener);
    };
  }, []);
  const [students, setStudents] = useState<Student[]>(initialStudents);
  const [removedStudents, setRemovedStudents] = useState<Student[]>(initialRemovedStudents);
  const [pendingOperations, setPendingOperations] = useState<Set<string>>(new Set());

  // State for accordion layout
  const [pendingStudents, setPendingStudents] = useState<PendingStudent[]>([]);
  const [existingStudents, setExistingStudents] = useState<Student[]>([]);
  const [studentClassAssignments, setStudentClassAssignments] = useState<Record<string, string[]>>({});
  const [existingStudentClassAssignments, setExistingStudentClassAssignments] = useState<Record<string, string[]>>({});

  const { set } = useMutation();

  // PERFORMANCE FIX: Optimize student updates to prevent excessive revalidations
  React.useEffect(() => {
    // Skip if no initial students or if we have pending operations
    if (!initialStudents.length || pendingOperations.size > 0) {
      return;
    }

    // Don't update if we have pending operations for these students
    const filteredActiveStudents = initialStudents.filter(student =>
      !pendingOperations.has(student.id)
    );

    // More efficient change detection using JSON comparison for small arrays
    const currentStudentsJson = JSON.stringify(students.map(s => s.id).sort());
    const newStudentsJson = JSON.stringify(filteredActiveStudents.map(s => s.id).sort());

    if (currentStudentsJson !== newStudentsJson) {
      setStudents(filteredActiveStudents);
    }
  }, [initialStudents.length, pendingOperations.size]); // Use primitive values to reduce re-runs

  // PERFORMANCE FIX: Optimize removed students updates to prevent excessive revalidations
  React.useEffect(() => {
    // Skip if no removed students or if we have pending operations
    if (!initialRemovedStudents.length || pendingOperations.size > 0) {
      return;
    }

    // Don't update if we have pending operations for these students
    const filteredRemovedStudents = initialRemovedStudents.filter(student =>
      !pendingOperations.has(student.id)
    );

    // More efficient change detection using JSON comparison for small arrays
    const currentRemovedJson = JSON.stringify(removedStudents.map(s => s.id).sort());
    const newRemovedJson = JSON.stringify(filteredRemovedStudents.map(s => s.id).sort());

    if (currentRemovedJson !== newRemovedJson) {
      setRemovedStudents(filteredRemovedStudents);
    }
  }, [initialRemovedStudents.length, pendingOperations.size]); // Use primitive values to reduce re-runs

  // PERFORMANCE FIX: Optimize event listeners to prevent excessive revalidations
  React.useEffect(() => {
    // Only set up listeners if we have a teacher
    if (!teacher?.id) return;

    const handleStudentCreated = (event: CustomEvent) => {
      // Add the new student to the local state immediately for instant UI update
      const newStudent = event.detail.student;
      if (newStudent && teacher) {
        const studentData: Student = {
          id: newStudent.id,
          name: newStudent.name,
          email: newStudent.email,
          username: newStudent.username,
          grade: newStudent.grade,
          isActive: true,
          teacherId: teacher.id,
          createdAt: new Date().toISOString()
        };

        setStudents(prev => {
          // Prevent duplicate additions
          if (prev.some(s => s.id === studentData.id)) {
            return prev;
          }
          return [...prev, studentData].sort((a, b) =>
            (a.name || '').localeCompare(b.name || '')
          );
        });

        // Also remove from pending students if it exists there
        setPendingStudents(prev => prev.filter(s => s.email !== newStudent.email));
      }
    };

    // Add optimistic update when student creation starts
    const handleStudentCreationStart = (event: CustomEvent) => {
      const studentData = event.detail;
      if (studentData) {
        // Add to pending students immediately for instant UI feedback
        const pendingStudent = {
          id: `pending_${Date.now()}`,
          name: studentData.name,
          email: studentData.email,
          generateUsername: studentData.generateUsername || false
        };

        setPendingStudents(prev => [...prev, pendingStudent]);
      }
    };

    const handleStudentUpdated = (event: CustomEvent) => {
      // Update the student in the local state immediately for instant UI update
      const updatedStudent = event.detail.student;
      if (updatedStudent) {
        setStudents(prev => prev.map(student =>
          student.id === updatedStudent.id
            ? { ...student, ...updatedStudent }
            : student
        ));
      }
    };

    // Handle accordion-specific events from command menu
    const handleExistingStudentFound = (event: CustomEvent) => {
      const existingStudent = event.detail.student;
      if (existingStudent) {
        setExistingStudents(prev => {
          // Prevent duplicates
          if (prev.some(s => s.id === existingStudent.id)) {
            return prev;
          }
          return [...prev, existingStudent];
        });
      }
    };

    // Listen for custom events from the student creation dialog and edit form
    window.addEventListener('studentCreated', handleStudentCreated as EventListener);
    window.addEventListener('studentUpdated', handleStudentUpdated as EventListener);
    window.addEventListener('existingStudentFound', handleExistingStudentFound as EventListener);
    window.addEventListener('studentCreationStart', handleStudentCreationStart as EventListener);

    return () => {
      window.removeEventListener('studentCreated', handleStudentCreated as EventListener);
      window.removeEventListener('studentUpdated', handleStudentUpdated as EventListener);
      window.removeEventListener('existingStudentFound', handleExistingStudentFound as EventListener);
      window.removeEventListener('studentCreationStart', handleStudentCreationStart as EventListener);
    };
  }, [teacher?.id]); // Only depend on teacher ID to reduce re-runs

  // Helper functions for accordion management
  const removeStudent = (studentId: string) => {
    setPendingStudents(prev => prev.filter(s => s.id !== studentId));
  };

  const removeExistingStudent = (studentId: string) => {
    setExistingStudents(prev => prev.filter(s => s.id !== studentId));
  };

  const handleEditStudent = (student: Student) => {
    setSelectedStudent(student);
  };

  const handleRemoveStudent = async (studentId: string) => {
    if (confirm('Are you sure you want to remove this student from your class?')) {
      try {
        // Mark as pending operation to prevent server data from overriding
        setPendingOperations(prev => new Set([...prev, studentId]));

        // Update the local state immediately for instant UI feedback
        setStudents(prev => prev.filter(s => s.id !== studentId));

        // Move the student to removed list immediately
        const studentToRemove = students.find(s => s.id === studentId);
        if (studentToRemove) {
          setRemovedStudents(prev => [...prev, { ...studentToRemove, isActive: false }]);
        }

        // Update the StudentTeacher relationship to inactive instead of modifying the user
        // This allows the student to remain active with other teachers
        await set.studentTeachers({
          with: {
            studentId: studentId,
            teacherId: teacher.id,
            status: 'active'
          },
          to: {
            status: 'inactive'
          }
        });

        // Clear pending operation after successful database update
        setPendingOperations(prev => {
          const newSet = new Set(prev);
          newSet.delete(studentId);
          return newSet;
        });


      } catch (error) {
        console.error('Error removing student:', error);

        // Clear pending operation and revert changes
        setPendingOperations(prev => {
          const newSet = new Set(prev);
          newSet.delete(studentId);
          return newSet;
        });

        // Revert the UI changes
        setStudents(initialStudents);
        setRemovedStudents(initialRemovedStudents);
        alert('Failed to remove student. Please try again.');
      }
    }
  };

  const handleRestoreStudent = async (studentId: string) => {
    try {
      // Mark as pending operation to prevent server data from overriding
      setPendingOperations(prev => new Set([...prev, studentId]));

      // Move student back to active list immediately
      const studentToRestore = removedStudents.find(s => s.id === studentId);
      if (studentToRestore) {
        setStudents(prev => [...prev, { ...studentToRestore, isActive: true }]);
        setRemovedStudents(prev => prev.filter(s => s.id !== studentId));
      }

      // Re-activate the StudentTeacher relationship
      await set.studentTeachers({
        with: {
          studentId: studentId,
          teacherId: teacher.id,
          status: 'inactive'
        },
        to: {
          status: 'active'
        }
      });

      // Clear pending operation after successful database update
      setPendingOperations(prev => {
        const newSet = new Set(prev);
        newSet.delete(studentId);
        return newSet;
      });


    } catch (error) {
      console.error('Error restoring student:', error);

      // Clear pending operation and revert changes
      setPendingOperations(prev => {
        const newSet = new Set(prev);
        newSet.delete(studentId);
        return newSet;
      });

      // Revert the UI changes
      setStudents(initialStudents);
      setRemovedStudents(initialRemovedStudents);
      alert('Failed to restore student. Please try again.');
    }
  };

  const handleToggleStudentStatus = async (student: Student) => {
    try {
      await set.users({
        with: { id: student.id },
        to: { isActive: !student.isActive }
      });
      // Update the local state to reflect the change immediately
      setStudents(prev => prev.map(s => 
        s.id === student.id ? { ...s, isActive: !s.isActive } : s
      ));
    } catch (error) {
      console.error('Error updating student status:', error);
      alert('Failed to update student status. Please try again.');
    }
  };

  return (
    <div>
      {/* Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as 'current' | 'removed')}
        className="w-full bg-muted rounded-lg"
      >
        <TabsList
          className="grid w-full grid-cols-2 p-2 mb-6"
          activeClassName="rounded-full border border-black/10 dark:border-white/10 shadow-sm shadow-black/10 dark:shadow-white/10 bg-gradient-to-r from-[#e1dfdf] via-[#d8d7d7] to-[#dad8d8] dark:from-[#242427] dark:via-[#232325] dark:to-[#202023]"
        >
          <TabsTrigger
            value="current"
            className="flex h-9 rounded-full items-center justify-center px-3 py-1.5 text-xs font-manrope_1 text-black/40 dark:text-white/40 data-[state=active]:text-black/80 dark:data-[state=active]:text-white/80 transition-all"
          >
            <Users className="w-4 h-4 mr-2" />
            All Students ({students.length + pendingStudents.length + existingStudents.length})
          </TabsTrigger>
          <TabsTrigger
            value="removed"
            className="flex h-9 rounded-full items-center justify-center px-3 py-1.5 text-xs font-manrope_1 text-black/40 dark:text-white/40 data-[state=active]:text-black/80 dark:data-[state=active]:text-white/80 transition-all"
          >
            <UserX className="w-4 h-4 mr-2" />
            Removed Students ({removedStudents.length})
          </TabsTrigger>
        </TabsList>

        <TabsContents className="rounded-sm h-full bg-background">
          <TabsContent value="current" className="flex-1 overflow-y-auto h-full">
            <CurrentStudentsTab
              students={students}
              onEditStudent={handleEditStudent}
              onRemoveStudent={handleRemoveStudent}
              onToggleStatus={handleToggleStudentStatus}
              teacherClasses={teacherClasses}
              pendingStudents={pendingStudents}
              existingStudents={existingStudents}
              studentClassAssignments={studentClassAssignments}
              existingStudentClassAssignments={existingStudentClassAssignments}
              removeStudent={removeStudent}
              removeExistingStudent={removeExistingStudent}
              teacher={teacher}
            />
          </TabsContent>

          <TabsContent value="removed" className="flex-1 overflow-y-auto h-full">
            <RemovedStudentsTab
              removedStudents={removedStudents}
              onRestoreStudent={handleRestoreStudent}
            />
          </TabsContent>
        </TabsContents>
      </Tabs>

      {/* Student Edit Modal */}
      {selectedStudent && (
        <StudentEditForm
          student={selectedStudent}
          isOpen={!!selectedStudent}
          onClose={() => setSelectedStudent(null)}
          availableGradeLevels={availableGradeLevels}
        />
      )}
    </div>
  );
}

// PERFORMANCE FIX: Export memoized component to prevent unnecessary re-renders
const MemoizedStudentManagementTabsWithData = memo(StudentManagementTabsWithData);

export default MemoizedStudentManagementTabsWithData;
