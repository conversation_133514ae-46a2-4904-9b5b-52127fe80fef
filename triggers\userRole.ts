﻿// triggers/userRole.ts
import type { AddTrigger, SetTrigger, RemoveTrigger } from 'blade/types';
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

 

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processUserRoleData = (userRoleData: any) => {
      console.log('UserRole during.add trigger - processing data:', userRoleData);

      // Set default values
      userRoleData.createdAt = userRoleData.createdAt || new Date();
      userRoleData.updatedAt = userRoleData.updatedAt || new Date();

      return userRoleData;
    };

    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processUserRoleData);
    } else {
      typedQuery.with = processUserRoleData(typedQuery.with);
    }

    console.log('UserRole during.add trigger - final query:', typedQuery);
    return typedQuery;
};
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

    

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // Update timestamp
    typedQuery.to.updatedAt = new Date();

    console.log('UserRole during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
};
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  

    // Add any validation or cleanup logic for deletions
    console.log('UserRole during.remove trigger called with query:', typedQuery);
    return typedQuery;
};
