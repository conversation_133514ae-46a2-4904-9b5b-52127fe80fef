# PowerShell script to check all trigger files for proper 'during' object structure

$triggerFiles = Get-ChildItem -Path "triggers" -Filter "*.ts" | Where-Object { $_.Name -ne "index.ts" }

Write-Host "Checking trigger files for proper 'during' object structure..."
Write-Host "================================================================"

$issues = @()

foreach ($file in $triggerFiles) {
    $filePath = $file.FullName
    $fileName = $file.Name
    
    Write-Host "`nChecking $fileName..."
    
    # Read the file content
    $content = Get-Content -Path $filePath -Raw
    
    # Check if file has 'during' export
    if ($content -notmatch "export const during = \{") {
        $issues += "$fileName: Missing 'export const during = {'"
        Write-Host "  ❌ Missing 'export const during = {'"
        continue
    }
    
    # Check for required operations in during object
    $hasAdd = $content -match "during = \{[^}]*add:"
    $hasSet = $content -match "during = \{[^}]*set:" -or $content -match "set: \("
    $hasRemove = $content -match "during = \{[^}]*remove:" -or $content -match "remove: \("
    
    Write-Host "  Operations found:"
    Write-Host "    add: $(if($hasAdd) { '✅' } else { '❌' })"
    Write-Host "    set: $(if($hasSet) { '✅' } else { '❌' })"
    Write-Host "    remove: $(if($hasRemove) { '✅' } else { '❌' })"
    
    if (-not $hasAdd) { $issues += "$fileName: Missing 'add' in during object" }
    if (-not $hasSet) { $issues += "$fileName: Missing 'set' in during object" }
    if (-not $hasRemove) { $issues += "$fileName: Missing 'remove' in during object" }
    
    # Check for async functions (which might cause issues)
    if ($content -match "async \(") {
        $issues += "$fileName: Contains async functions in during triggers (should be synchronous)"
        Write-Host "  ⚠️  Contains async functions (should be synchronous)"
    }
    
    # Check for individual exports (conflicts)
    if ($content -match "^export const (add|set|get|remove):" -and $content -notmatch "REMOVED: Individual trigger exports") {
        $issues += "$fileName: Still contains individual trigger exports"
        Write-Host "  ❌ Still contains individual trigger exports"
    }
}

Write-Host "`n================================================================"
Write-Host "SUMMARY:"
Write-Host "================================================================"

if ($issues.Count -eq 0) {
    Write-Host "✅ All trigger files look good!"
} else {
    Write-Host "❌ Found $($issues.Count) issues:"
    foreach ($issue in $issues) {
        Write-Host "  - $issue"
    }
}

Write-Host "`nDone checking trigger files."
