//logout-section.client.tsx
'use client';

import React, { useState } from 'react';
import type {MouseEvent } from 'react';
import { LogOut, Check, X } from 'lucide-react';
import { useUnifiedSession } from '../../../../lib/auth-client';
import { useRedirect } from 'blade/hooks';
import { useSharedSidebarState } from '../../../../stores/sidebar-store.client';
import { Link } from 'blade/client/components';
import { cn } from '../../../../lib/utils'; // Assuming you have this utility
import { CloseIcon, SignoutIcon, CheckIcon } from "../../../ui/icons"


interface LogoutSectionProps {
  onLogout?: () => void;
}

export function LogoutSection({ onLogout }: LogoutSectionProps) {
  const { signOut, session } = useUnifiedSession();
  const redirect = useRedirect();
  const sidebarState = useSharedSidebarState();
  const [isSigningOut, setIsSigningOut] = useState(false);
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(true); // Start with confirmation showing
  
  // Spotlight effect state
  const [logoutPosition, setLogoutPosition] = useState({ x: 0, y: 0 });
  const [logoutOpacity, setLogoutOpacity] = useState(0);
  const [cancelPosition, setCancelPosition] = useState({ x: 0, y: 0 });
  const [cancelOpacity, setCancelOpacity] = useState(0);

  const handleLogoutMouseMove = (e: MouseEvent<HTMLButtonElement>) => {
    const { left, top } = e.currentTarget.getBoundingClientRect();
    setLogoutPosition({ x: e.clientX - left, y: e.clientY - top });
  };

   const handleCancelMouseMove = (e: MouseEvent<HTMLButtonElement>) => {
    const { left, top } = e.currentTarget.getBoundingClientRect();
    setCancelPosition({ x: e.clientX - left, y: e.clientY - top });
  };

  const handleLogoutMouseEnter = () => setLogoutOpacity(1);
  const handleLogoutMouseLeave = () => setLogoutOpacity(0);
  const handleCancelMouseEnter = () => setCancelOpacity(1);
  const handleCancelMouseLeave = () => setCancelOpacity(0);

  const handleLogout = async () => {
    console.log('Logout button clicked - starting sign out process');

    // Determine the user's role for redirect
    const userRole = session?.user?.role;
    const loginUrl = userRole ? `/login?role=${userRole}` : '/login';

    console.log('User role for logout redirect:', userRole, '-> URL:', loginUrl);

    // Set signing out state to trigger prefetching
    setIsSigningOut(true);

    // Clear all sidebar and flyout states before signing out
    if (sidebarState.clearAllSidebarStates) {
      sidebarState.clearAllSidebarStates();
    }

    // Call parent onLogout if provided (to close flyout)
    if (onLogout) {
      onLogout();
    }

    // Sign out with prefetching - shorter delay since login page is prefetched
    await signOut();

    // Small delay to let prefetching work, then redirect to role-specific login
    setTimeout(() => {
      redirect(loginUrl);
    }, 100);

    console.log('Sign out complete with prefetched redirect to:', loginUrl);
  };

  // Prefetch role-specific login page when signing out
  const userRole = session?.user?.role;
  const loginUrl = userRole ? `/login?role=${userRole}` : '/login';

  const PrefetchLoginLink = isSigningOut ? (
    <Link href={loginUrl} prefetch={true} className="hidden">
      <a>Prefetch login</a>
    </Link>
  ) : null;

  return (
    <>
      {PrefetchLoginLink}
      <div className="p-2 md:p-4 overflow-hidden">
        {showLogoutConfirm ? (
          <div className="flex py-1 items-center justify-between">
            {/* Question text */}
            <p className="text-xs md:text-sm font-manrope_1 text-red-700 dark:text-red-300">
              Are you sure you want to sign out?
            </p>
            
            {/* Button container */}
            <div className="flex gap-2">
              {/* Apply the sophisticated design to the main logout button */}
              <button
                onClick={() => setShowLogoutConfirm(false)} // Transform to main button
                onMouseMove={handleLogoutMouseMove}
                onMouseEnter={handleLogoutMouseEnter}
                onMouseLeave={handleLogoutMouseLeave}
                disabled={isSigningOut}
                className={cn(
                  'group cursor-pointer relative flex flex-1 items-center justify-center overflow-hidden rounded-full border border-red-500/20 bg-gradient-to-r from-red-900/20 to-red-950/20 px-3 py-1.5 h-8 text-sm font-light text-red-300 shadow-md transition-all duration-300 hover:border-red-500/30 hover:text-red-200 focus:outline-none',
                  'disabled:cursor-not-allowed disabled:opacity-50',
                  'dark:border-red-500/20 dark:bg-gradient-to-r dark:from-red-900/20 dark:to-red-950/20 dark:text-red-300 dark:hover:border-red-500/30 dark:hover:text-red-200',
                  // Light mode styles
                  'border-red-300/40 bg-gradient-to-r from-red-100/60 to-red-200/60 text-red-700 hover:border-red-400/50 hover:text-red-800'
                )}
              >
                <div
                  key="logout-spotlight"
                  className="pointer-events-none absolute -inset-px rounded-lg bg-red-500/20 transition-opacity duration-300"
                  style={{
                    opacity: logoutOpacity,
                    maskImage: `radial-gradient(250px circle at ${logoutPosition.x}px ${logoutPosition.y}px, black 45%, transparent)`,
                    WebkitMaskImage: `radial-gradient(250px circle at ${logoutPosition.x}px ${logoutPosition.y}px, black 45%, transparent)`,
                  }}
                />
                <div className="relative z-10 items-center">
                  <span className="hidden md:inline">Yes</span>
                  <CheckIcon className="w-4 h-4 md:hidden" />
                </div>
              </button>
              
              {/* Cancel button with simpler styling to match */}
              <button
                onClick={() => onLogout && onLogout()} // Close the dialog
                onMouseMove={handleCancelMouseMove}
                onMouseEnter={handleCancelMouseEnter}
                onMouseLeave={handleCancelMouseLeave}
                className={cn(
                  'group cursor-pointer relative flex flex-1 items-center justify-center overflow-hidden rounded-full border border-zinc-500/20 bg-gradient-to-r from-zinc-900/20 to-zinc-950/20 px-3 py-1.5 h-8 text-sm font-light text-zinc-300 shadow-md transition-all duration-300 hover:border-zinc-500/30 hover:text-zinc-200 focus:outline-none',
                  'disabled:cursor-not-allowed disabled:opacity-50',
                  'dark:border-zinc-500/20 dark:bg-gradient-to-r dark:from-zinc-900/20 dark:to-zinc-950/20 dark:text-zinc-300 dark:hover:border-zinc-500/30 dark:hover:text-zinc-200',
                  // Light mode styles
                  'border-zinc-300/40 bg-gradient-to-r from-zinc-100/60 to-zinc-200/60 text-zinc-700 hover:border-zinc-400/50 hover:text-zinc-800'
                )}
              >
                <div
                  key="cancel-spotlight"
                  className="pointer-events-none absolute -inset-px rounded-lg bg-zinc-500/20 transition-opacity duration-300"
                  style={{
                    opacity: cancelOpacity,
                    maskImage: `radial-gradient(250px circle at ${cancelPosition.x}px ${cancelPosition.y}px, black 45%, transparent)`,
                    WebkitMaskImage: `radial-gradient(250px circle at ${cancelPosition.x}px ${cancelPosition.y}px, black 45%, transparent)`,
                  }}
                />
                <div className="relative z-10 items-center">
                  <span className="hidden md:inline">Cancel</span>
                  <CloseIcon className="w-4 h-4 md:hidden" />
                </div>
              </button>
            </div>
          </div>
        ) : (
          <div className="flex py-1 items-center justify-between">
            {/* Question text remains visible */}
            <p className="text-xs md:text-sm font-manrope_1 text-red-700 dark:text-red-300">
              Hope to see you again soon.
            </p>
            
            {/* Single Sign Out button replaces Yes/Cancel buttons */}
            <div className="flex gap-2">
              <button
                onClick={handleLogout} // Now this actually performs the logout
                onMouseMove={handleLogoutMouseMove}
                onMouseEnter={handleLogoutMouseEnter}
                onMouseLeave={handleLogoutMouseLeave}
                disabled={isSigningOut}
                className={cn(
              'group cursor-pointer w-full relative flex flex-1 items-center justify-between overflow-hidden rounded-full border border-red-500/20 bg-gradient-to-r from-red-900/20 to-red-950/20 px-3 py-1.5 h-8 text-sm font-light text-red-300 shadow-md  hover:border-red-500/30 hover:text-red-200 focus:outline-none',
              'disabled:cursor-not-allowed disabled:opacity-50',
              'dark:border-red-500/20 dark:bg-gradient-to-r dark:from-red-900/20 dark:to-red-950/20 dark:text-red-300 dark:hover:border-red-500/30 dark:hover:text-red-200',
              // Light mode styles
              'border-red-300/40 bg-gradient-to-r from-red-100/60 to-red-200/60 text-red-700 hover:border-red-400/50 hover:text-red-800'
            )}
          >
            <div
              key="logout-spotlight"
              className="pointer-events-none absolute -inset-px rounded-lg bg-red-500/20 transition-opacity duration-300"
              style={{
                opacity: logoutOpacity,
                maskImage: `radial-gradient(250px circle at ${logoutPosition.x}px ${logoutPosition.y}px, black 45%, transparent)`,
                WebkitMaskImage: `radial-gradient(250px circle at ${logoutPosition.x}px ${logoutPosition.y}px, black 45%, transparent)`,
              }}
            />
                <div className="relative z-10 flex items-center gap-2">
                 
                  <SignoutIcon className="h-4 w-4" />
                   <span className="text-xs font-medium font-manrope_1">
                    {isSigningOut ? 'Signing out...' : 'Sign Out'}
                  </span>
                </div>
              </button>
            </div>
          </div>
        )}
      </div>
    </>
  );
}