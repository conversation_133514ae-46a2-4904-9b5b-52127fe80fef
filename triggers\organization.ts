﻿// triggers/organization.ts
import type { Add<PERSON>rigger, SetTrigger, RemoveTrigger } from 'blade/types';
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processOrganizationData = (orgData: any) => {
      console.log('Organization during.add trigger - processing data:', orgData);

      // Auto-generate slug if not provided
      if (!orgData.slug && orgData.name) {
        orgData.slug = orgData.name
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[^a-z0-9-]/g, '-')
          .replace(/-+/g, '-')
          .replace(/^-|-$/g, '') || 'organization';
      }

      // Set default values
      orgData.createdAt = orgData.createdAt || new Date();
      orgData.updatedAt = orgData.updatedAt || new Date();

      console.log('Organization during.add trigger - processed data:', orgData);
      return orgData;
    };

    // Handle array of organizations
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processOrganizationData);
    } else {
      // Handle single organization
      typedQuery.with = processOrganizationData(typedQuery.with);
    }

    return typedQuery;
};
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

 

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // Update timestamp
    typedQuery.to.updatedAt = new Date();

    // Update slug if name is being changed and slug is not already provided
    if (typedQuery.to.name && !typedQuery.to.slug) {
      typedQuery.to.slug = typedQuery.to.name
        .toLowerCase()
        .replace(/\s+/g, '-')
        .replace(/[^a-z0-9-]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '') || 'organization';
    }

    console.log('Organization during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
};
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

 
    // Add any validation or cleanup logic for deletions
    console.log('Organization during.remove trigger called with query:', typedQuery);
    return typedQuery;
};
