// lib/auth-client.ts - Unified Better Auth client
import { createAuthClient } from "better-auth/react";
import { emailOTPClient, usernameClient, adminClient, organizationClient } from "better-auth/client/plugins";
import { useMemo, useState } from 'react';
import type { 
  ExtendedSession, 
  UseUnifiedSessionReturn, 
  UserRole,
  ExtendedUser,
  StudentUser,
  TeacherUser,
  SchoolAdminUser
} from './types/auth';

// Create a single unified auth client with optimized configuration
export const authClient = createAuthClient({
  baseURL: typeof window !== 'undefined' ? window.location.origin : "http://localhost:3000",
  basePath: "/api/auth", // Single base path for all authentication
  fetchOptions: {
    credentials: "include"
  },
  plugins: [
    usernameClient(), // For students
    emailOTPClient(), // For teachers and school admins
    adminClient(), // For admin functionality
    organizationClient() // For school organizations
  ]
});

// Export all auth methods
export const {
  signIn,
  signUp,
  signOut: betterAuthSignOut,
  getSession,
  useSession,
  emailOtp,
  admin,
  organization
} = authClient;

/**
 * A unified session hook that handles all user roles in a single Better Auth instance.
 * It transforms the session data to match our extended user types.
 */
export const useUnifiedSession = (): UseUnifiedSessionReturn => {
  const { data: session, isPending: loading, refetch } = useSession();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  // Create a stable, memoized transform function
  const transformSessionData = (sessionData: typeof session): ExtendedSession | null => {
    if (!sessionData?.user) {
      return null;
    }

    const user = sessionData.user as any;

    // Additional validation: if user data seems incomplete, treat as invalid session
    if (!user.id || !user.role) {
      console.log('Invalid session data detected, user missing required fields');
      return null;
    }

    const role = user.role as UserRole;

    let extendedUser: ExtendedUser;

    switch (role) {
      case 'school_admin':
        extendedUser = {
          ...user,
          role: 'school_admin' as const,
          slug: user.slug || user.id,
          schoolName: user.schoolName,
          schoolAddress: user.schoolAddress,
          schoolPlaceId: user.schoolPlaceId,
          schoolType: user.schoolType,
          schoolDistrict: user.schoolDistrict,
          studentCount: user.studentCount,
          teacherCount: user.teacherCount,
        } as SchoolAdminUser;
        break;

      case 'teacher':
        extendedUser = {
          ...user,
          role: 'teacher' as const,
          slug: user.slug || user.id,
          isIndependent: user.isIndependent ?? true,
          schoolId: user.schoolId,
          department: user.department,
          subjects: user.subjects,
          isVerified: user.isVerified ?? false,
        } as TeacherUser;
        break;

      case 'student':
      default:
        extendedUser = {
          ...user,
          role: 'student' as const,
          slug: user.slug || user.id,
          teacherId: user.teacherId || '',
          isActive: user.isActive ?? false,
          classId: user.classId,
          grade: user.grade,
        } as StudentUser;
        break;
    }

    return {
      user: extendedUser,
      session: sessionData.session
    };
  };

  const signOut = async () => {
    setIsLoggingOut(true);
    try {
      await betterAuthSignOut();
    } catch (error) {
      console.error('An error occurred during sign out:', error);
      // Optionally re-throw or handle the error as needed
    } finally {
      setIsLoggingOut(false);
    }
  };

  const refreshSession = async (): Promise<ExtendedSession | null> => {
    try {
      console.log('🔄 Starting session refresh...');

      // First, invalidate the current session cache
      await refetch(); // Invalidate and trigger a background refetch

      // Add a longer delay to allow database changes to propagate
      // This is especially important after user creation/updates
      await new Promise(resolve => setTimeout(resolve, 500));

      // Force a database fetch by disabling cookie cache
      console.log('🔄 Fetching fresh session from database...');
      const response = await getSession({
        query: {
          disableCookieCache: true
        }
      }); // Manually get the latest session

      if (response.error) {
        console.error('❌ Error refreshing session:', response.error);
        return null;
      }

      const transformedSession = transformSessionData(response.data);
      console.log('✅ Session refresh completed:', transformedSession ? {
        userId: transformedSession.user.id,
        role: transformedSession.user.role,
        slug: transformedSession.user.slug
      } : 'null');

      return transformedSession;
    } catch (error) {
      console.error('❌ Error in refreshSession:', error);
      return null;
    }
  };

  // Memoize the transformed session and the refresh function together
  // Only re-compute when session data actually changes, not on every render
  const unifiedSessionResult = useMemo((): UseUnifiedSessionReturn => {
    const transformed = transformSessionData(session);

    return {
      session: transformed,
      signOut,
      refreshSession,
      isLoggingOut,
      loading: loading || isLoggingOut, // Combine loading states
      role: transformed?.user.role || null,
    };
  }, [
    session?.user?.id, // Only re-compute when user ID changes
    session?.user?.role, // Or when role changes
    session?.user?.slug, // Or when slug changes
    session?.user?.image, // Or when image changes (important for avatar updates)
    session?.session?.id, // Or when session ID changes
    loading,
    isLoggingOut
  ]);

  return unifiedSessionResult;
};

// Role-specific hooks for convenience
export const useStudentAuth = () => {
  const { session, signOut, loading, refreshSession, isLoggingOut, role } = useUnifiedSession();
  return {
    session: session?.user.role === 'student' ? session : null,
    signOut,
    loading,
    isLoggingOut,
    refreshSession,
    role: 'student' as const,
  };
};

export const useTeacherAuth = () => {
  const { session, signOut, loading, refreshSession, isLoggingOut, role } = useUnifiedSession();
  return {
    session: session?.user.role === 'teacher' ? session : null,
    signOut,
    loading,
    isLoggingOut,
    refreshSession,
    role: 'teacher' as const,
  };
};

export const useSchoolAuth = () => {
  const { session, signOut, loading, refreshSession, isLoggingOut, role } = useUnifiedSession();
  return {
    session: session?.user.role === 'school_admin' ? session : null,
    signOut,
    loading,
    isLoggingOut,
    refreshSession,
    role: 'school_admin' as const,
  };
};