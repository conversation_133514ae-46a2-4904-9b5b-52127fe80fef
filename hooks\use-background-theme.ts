// hooks/use-background-theme.ts
import { useState, useEffect, useCallback } from 'react';
import { useTheme } from '../components/providers/theme-provider.client';

// Define background themes with more visible light mode colors
export interface BackgroundTheme {
  id: string;
  name: string;
  lightStyle: React.CSSProperties;
  darkStyle: React.CSSProperties;
  preview: {
    light: string;
    dark: string;
  };
}

export const backgroundThemes: BackgroundTheme[] = [
  {
    id: 'default',
    name: 'Default',
    lightStyle: {
      background: "linear-gradient(to bottom, #f2f2f2, #e8e8e8, #f2f2f2)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #101012, #18181a, #101012)"
    },
    preview: {
      light: "linear-gradient(to bottom, #f2f2f2, #e8e8e8, #f2f2f2)",
      dark: "linear-gradient(to bottom, #101012, #18181a, #101012)"
    }
  },
  {
    id: 'horizon-glow-bottom',
    name: '<PERSON> Glow (Bottom)',
    lightStyle: {
      background: "linear-gradient(to bottom, #f2f2f2 20%, #eaf4f7 100%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #101012 20%, #0e1621 100%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #f2f2f2 20%, #eaf4f7 100%)",
      dark: "linear-gradient(to bottom, #101012 20%, #0e1621 100%)"
    }
  },
  {
    id: 'crimson-depth-bottom',
    name: 'Crimson Depth (Bottom)',
    lightStyle: {
      background: "linear-gradient(to bottom, #f2f2f2 20%, #f7e8ea 100%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #101012 20%, #1f1315 100%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #f2f2f2 20%, #f7e8ea 100%)",
      dark: "linear-gradient(to bottom, #101012 20%, #1f1315 100%)"
    }
  },
  {
    id: 'emerald-void-bottom',
    name: 'Emerald Void (Bottom)',
    lightStyle: {
      background: "linear-gradient(to bottom, #f2f2f2 20%, #e8f3e9 100%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #101012 20%, #0f1a11 100%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #f2f2f2 20%, #e8f3e9 100%)",
      dark: "linear-gradient(to bottom, #101012 20%, #0f1a11 100%)"
    }
  },
  {
    id: 'violet-abyss-bottom',
    name: 'Violet Abyss (Bottom)',
    lightStyle: {
      background: "linear-gradient(to bottom, #f2f2f2 20%, #f0e7f4 100%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #101012 20%, #1a1321 100%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #f2f2f2 20%, #f0e7f4 100%)",
      dark: "linear-gradient(to bottom, #101012 20%, #1a1321 100%)"
    }
  },
  {
    id: 'azure-depths-bottom',
    name: 'Azure Depths (Bottom)',
    lightStyle: {
      background: "linear-gradient(to bottom, #f2f2f2 20%, #eaebf5 100%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #101012 20%, #0e0e2a 100%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #f2f2f2 20%, #eaebf5 100%)",
      dark: "linear-gradient(to bottom, #101012 20%, #0e0e2a 100%)"
    }
  },
  {
    id: 'orchid-depths-bottom',
    name: 'Orchid Depths (Bottom)',
    lightStyle: {
      background: "linear-gradient(to bottom, #f2f2f2 20%, #f8e8ed 100%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #101012 20%, #2a1220 100%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #f2f2f2 20%, #f8e8ed 100%)",
      dark: "linear-gradient(to bottom, #101012 20%, #2a1220 100%)"
    }
  },
  {
    id: 'horizon-glow-top',
    name: 'Horizon Glow (Top)',
    lightStyle: {
      background: "linear-gradient(to bottom, #eaf4f7 0%, #f2f2f2 80%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #0e1621 0%, #101012 80%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #eaf4f7 0%, #f2f2f2 80%)",
      dark: "linear-gradient(to bottom, #0e1621 0%, #101012 80%)"
    }
  },
  {
    id: 'crimson-depth-top',
    name: 'Crimson Depth (Top)',
    lightStyle: {
      background: "linear-gradient(to bottom, #f7e8ea 0%, #f2f2f2 80%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #1f1315 0%, #101012 80%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #f7e8ea 0%, #f2f2f2 80%)",
      dark: "linear-gradient(to bottom, #1f1315 0%, #101012 80%)"
    }
  },
  {
    id: 'emerald-void-top',
    name: 'Emerald Void (Top)',
    lightStyle: {
      background: "linear-gradient(to bottom, #e8f3e9 0%, #f2f2f2 80%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #0f1a11 0%, #101012 80%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #e8f3e9 0%, #f2f2f2 80%)",
      dark: "linear-gradient(to bottom, #0f1a11 0%, #101012 80%)"
    }
  },
  {
    id: 'violet-abyss-top',
    name: 'Violet Abyss (Top)',
    lightStyle: {
      background: "linear-gradient(to bottom, #f0e7f4 0%, #f2f2f2 80%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #1a1321 0%, #101012 80%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #f0e7f4 0%, #f2f2f2 80%)",
      dark: "linear-gradient(to bottom, #1a1321 0%, #101012 80%)"
    }
  },
  {
    id: 'azure-depths-top',
    name: 'Azure Depths (Top)',
    lightStyle: {
      background: "linear-gradient(to bottom, #eaebf5 0%, #f2f2f2 80%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #0e0e2a 0%, #101012 80%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #eaebf5 0%, #f2f2f2 80%)",
      dark: "linear-gradient(to bottom, #0e0e2a 0%, #101012 80%)"
    }
  },
  {
    id: 'orchid-depths-top',
    name: 'Orchid Depths (Top)',
    lightStyle: {
      background: "linear-gradient(to bottom, #f8e8ed 0%, #f2f2f2 80%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #2a1220 0%, #101012 80%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #f8e8ed 0%, #f2f2f2 80%)",
      dark: "linear-gradient(to bottom, #2a1220 0%, #101012 80%)"
    }
  }
];

export interface BackgroundThemeStyle {
  background: string;
}

export function useBackgroundTheme() {
  const { actualTheme, mounted } = useTheme();
  const [currentBackground, setCurrentBackground] = useState<string>('default');
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize background from localStorage
  useEffect(() => {
    if (!mounted || typeof window === 'undefined') return;

    const savedBackground = localStorage.getItem('background-theme');
    if (savedBackground && backgroundThemes.find(t => t.id === savedBackground)) {
      setCurrentBackground(savedBackground);
    } else {
      // Set default and save it
      localStorage.setItem('background-theme', 'default');
      setCurrentBackground('default');
    }
    
    setIsInitialized(true);
  }, [mounted]);

  // Listen for background theme changes from other components
  useEffect(() => {
    if (!mounted || typeof window === 'undefined') return;

    const handleBackgroundThemeChange = (event: CustomEvent) => {
      const { backgroundId } = event.detail;
      if (backgroundId && backgroundThemes.find(t => t.id === backgroundId)) {
        setCurrentBackground(backgroundId);
        localStorage.setItem('background-theme', backgroundId);
      }
    };

    window.addEventListener('backgroundThemeChanged', handleBackgroundThemeChange as EventListener);

    return () => {
      window.removeEventListener('backgroundThemeChanged', handleBackgroundThemeChange as EventListener);
    };
  }, [mounted]);

  // Get current background style
  const getBackgroundStyle = useCallback((): BackgroundThemeStyle => {
    if (!isInitialized) {
      // Return default style while initializing
      return { background: "linear-gradient(to right, #f2f2f2, #e8e8e8, #eeeeee)" };
    }

    const theme = backgroundThemes.find(t => t.id === currentBackground) || backgroundThemes[0];
    
    if (!theme) {
      return { background: "linear-gradient(to right, #f2f2f2, #e8e8e8, #eeeeee)" };
    }
         
    if (actualTheme === 'dark') {
      return { background: theme.darkStyle.background as string };
    } else {
      return { background: theme.lightStyle.background as string };
    }
  }, [currentBackground, actualTheme, isInitialized]);

  // Method to change background (for components to use)
  const setBackground = useCallback((backgroundId: string) => {
    if (backgroundThemes.find(t => t.id === backgroundId)) {
      setCurrentBackground(backgroundId);
      localStorage.setItem('background-theme', backgroundId);
      
      // Dispatch event for other components
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('backgroundThemeChanged', {
          detail: { backgroundId }
        }));
      }
    }
  }, []);

  return {
    currentBackground,
    backgroundStyle: getBackgroundStyle(),
    setBackground,
    mounted: mounted && isInitialized,
    actualTheme
  };
}