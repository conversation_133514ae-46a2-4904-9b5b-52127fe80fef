// pages/teacher/layout.tsx (Server Component - NO hooks allowed)
import { LayoutWrapper } from '../../components/auth/teacher/dual-sidebar';
import { AttentionProvider } from 'react-attention';

const TeacherLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <AttentionProvider>
      <LayoutWrapper
        showUserNav={true}
        showHeader={true}
      >
        {children}
      </LayoutWrapper>
    </AttentionProvider>
  );
};

export default TeacherLayout;