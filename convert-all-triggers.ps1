# PowerShell script to convert all trigger files from 'during' object to individual exports
# This fixes the TRIGGER_REQUIRED error by using the correct Blade trigger structure

$triggerFiles = Get-ChildItem -Path "triggers" -Filter "*.ts" | Where-Object { $_.Name -ne "index.ts" }

Write-Host "Converting all trigger files to individual exports..."
Write-Host "================================================================"

foreach ($file in $triggerFiles) {
    $filePath = $file.FullName
    $fileName = $file.Name
    
    # Read the file content
    $content = Get-Content -Path $filePath -Raw
    
    # Skip if already converted (doesn't have 'during' object)
    if ($content -notmatch "export const during = \{") {
        Write-Host "  $fileName - Already converted, skipping..."
        continue
    }
    
    Write-Host "  $fileName - Converting..."
    
    # Start building new content
    $newContent = @"
// triggers/$fileName
import type { <PERSON>d<PERSON><PERSON><PERSON>, Set<PERSON>rigger, RemoveTrigger } from 'blade/types';

"@
    
    # Extract add trigger logic
    if ($content -match "add: \([^)]*\) => \{([\s\S]*?)\n  \},") {
        $addLogic = $matches[1]
        $newContent += @"
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;
$addLogic
};

"@
    }
    
    # Extract set trigger logic
    if ($content -match "set: \([^)]*\) => \{([\s\S]*?)\n  \},") {
        $setLogic = $matches[1]
        $newContent += @"
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;
$setLogic
};

"@
    }
    
    # Extract remove trigger logic
    if ($content -match "remove: \([^)]*\) => \{([\s\S]*?)\n  \}") {
        $removeLogic = $matches[1]
        $newContent += @"
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;
$removeLogic
};
"@
    }
    
    # Write the new content to the file
    Set-Content -Path $filePath -Value $newContent -Encoding UTF8
    Write-Host "    ✅ Converted $fileName"
}

Write-Host "`n================================================================"
Write-Host "✅ All trigger files converted to individual exports!"
Write-Host "================================================================"
