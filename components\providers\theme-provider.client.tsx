// components/providers/theme-provider.client.tsx
'use client';

import type { ReactNode } from 'react';
import { createContext, useContext, useEffect, useState } from 'react';
import { useCookie } from 'blade/hooks';

export type Theme = 'light' | 'dark' | 'system';

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  actualTheme: 'light' | 'dark';
  mounted: boolean;
  backgroundTheme: string;
  setBackgroundTheme: (backgroundId: string) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const [theme, setThemeState] = useState<Theme>('light');
  const [actualTheme, setActualTheme] = useState<'light' | 'dark'>('light');
  const [backgroundTheme, setBackgroundThemeState] = useState<string>('default');
  const [mounted, setMounted] = useState(false);
  const [themeCookie, setThemeCookie] = useCookie<Theme>('theme');

  // Apply theme to DOM with proper class management
  const applyThemeToDOM = (resolvedTheme: 'light' | 'dark', immediate = false) => {
    if (typeof window === 'undefined') return;

    const htmlElement = document.documentElement;
    
    // Prevent flash during theme changes
    if (immediate) {
      const css = document.createElement('style');
      css.textContent = `
        *, *::before, *::after { 
          transition-duration: 0s !important; 
          animation-duration: 0s !important; 
        }
      `;
      document.head.appendChild(css);
      
      // Force reflow and remove after applying theme
      requestAnimationFrame(() => {
        if (resolvedTheme === 'dark') {
          htmlElement.classList.add('dark');
          htmlElement.classList.remove('light');
        } else {
          htmlElement.classList.remove('dark');
          htmlElement.classList.add('light');
        }
        
        htmlElement.setAttribute('data-theme', resolvedTheme);
        
        // Remove the transition-blocking CSS
        requestAnimationFrame(() => {
          if (css.parentNode) {
            css.parentNode.removeChild(css);
          }
        });
      });
    } else {
      if (resolvedTheme === 'dark') {
        htmlElement.classList.add('dark');
        htmlElement.classList.remove('light');
      } else {
        htmlElement.classList.remove('dark');
        htmlElement.classList.add('light');
      }
      htmlElement.setAttribute('data-theme', resolvedTheme);
    }
  };

  // Resolve theme based on current theme setting
  const resolveTheme = (themeValue: Theme): 'light' | 'dark' => {
    if (themeValue === 'system') {
      if (typeof window === 'undefined') return 'light';
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return themeValue as 'light' | 'dark';
  };

  // Initialize theme and background on mount - prevent hydration issues
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Initialize theme
    const savedTheme = localStorage.getItem('theme') as Theme;
    const cookieTheme = themeCookie;
    
    let initialTheme: Theme = 'light';
    
    // Priority: localStorage > cookie > default
    if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
      initialTheme = savedTheme;
    } else if (cookieTheme && ['light', 'dark', 'system'].includes(cookieTheme)) {
      initialTheme = cookieTheme;
    }

    // Resolve actual theme first
    const resolvedTheme = resolveTheme(initialTheme);

    // Initialize background theme based on resolved theme
    const savedBackground = localStorage.getItem('background-theme') || 'default';

    // Use mode-specific background if available, otherwise fall back to general background
    const initialBackground = resolvedTheme === 'dark'
      ? (localStorage.getItem('background-theme-dark') || savedBackground)
      : (localStorage.getItem('background-theme-light') || savedBackground);

    // Apply theme immediately to prevent flash
    applyThemeToDOM(resolvedTheme, true);

    // Update all state at once
    setThemeState(initialTheme);
    setActualTheme(resolvedTheme);
    setBackgroundThemeState(initialBackground);

    // Sync storage
    localStorage.setItem('theme', initialTheme);
    localStorage.setItem('background-theme', initialBackground);
    // Also store in the mode-specific key
    if (resolvedTheme === 'dark') {
      localStorage.setItem('background-theme-dark', initialBackground);
    } else {
      localStorage.setItem('background-theme-light', initialBackground);
    }
    setThemeCookie(initialTheme, { client: true });

    setMounted(true);

    console.log('Theme initialized:', { initialTheme, resolvedTheme, savedBackground });
  }, []); // Only run once on mount

  // Handle system theme changes when in system mode
  useEffect(() => {
    if (!mounted || typeof window === 'undefined' || theme !== 'system') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleSystemChange = (e: MediaQueryListEvent) => {
      const newResolvedTheme = e.matches ? 'dark' : 'light';
      setActualTheme(newResolvedTheme);
      applyThemeToDOM(newResolvedTheme);
      console.log('System theme changed to:', newResolvedTheme);
    };

    mediaQuery.addEventListener('change', handleSystemChange);
    return () => mediaQuery.removeEventListener('change', handleSystemChange);
  }, [theme, mounted]);

  // Listen for theme changes from other components/windows
  useEffect(() => {
    if (!mounted || typeof window === 'undefined') return;

    const handleThemeChange = (event: CustomEvent) => {
      const newTheme = event.detail.theme as Theme;
      if (newTheme !== theme && ['light', 'dark', 'system'].includes(newTheme)) {
        const resolvedTheme = resolveTheme(newTheme);
        setThemeState(newTheme);
        setActualTheme(resolvedTheme);
        applyThemeToDOM(resolvedTheme);
        console.log('External theme change:', { newTheme, resolvedTheme });
      }
    };

    const handleBackgroundChange = (event: CustomEvent) => {
      const { backgroundId } = event.detail;
      if (backgroundId !== backgroundTheme) {
        setBackgroundThemeState(backgroundId);
        console.log('External background change:', backgroundId);
      }
    };

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'theme' && e.newValue && e.newValue !== theme) {
        const newTheme = e.newValue as Theme;
        if (['light', 'dark', 'system'].includes(newTheme)) {
          const resolvedTheme = resolveTheme(newTheme);
          setThemeState(newTheme);
          setActualTheme(resolvedTheme);
          applyThemeToDOM(resolvedTheme);
        }
      } else if (e.key === 'background-theme' && e.newValue && e.newValue !== backgroundTheme) {
        setBackgroundThemeState(e.newValue);
      }
    };

    window.addEventListener('themeChanged', handleThemeChange as EventListener);
    window.addEventListener('backgroundThemeChanged', handleBackgroundChange as EventListener);
    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('themeChanged', handleThemeChange as EventListener);
      window.removeEventListener('backgroundThemeChanged', handleBackgroundChange as EventListener);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [theme, backgroundTheme, mounted]);

  // Theme setter with immediate persistence and DOM application
  const setTheme = (newTheme: Theme) => {
    if (newTheme === theme) return;

    const resolvedTheme = resolveTheme(newTheme);

    // Update state
    setThemeState(newTheme);
    setActualTheme(resolvedTheme);

    // Apply to DOM immediately
    applyThemeToDOM(resolvedTheme);

    // Persist immediately
    localStorage.setItem('theme', newTheme);
    setThemeCookie(newTheme, { client: true });

    // Notify other components/windows
    window.dispatchEvent(new CustomEvent('themeChanged', {
      detail: { theme: newTheme }
    }));

    console.log('Theme set:', { newTheme, resolvedTheme });
  };

  // Background theme setter
  const setBackgroundTheme = (backgroundId: string) => {
    if (backgroundId === backgroundTheme) return;

    setBackgroundThemeState(backgroundId);
    localStorage.setItem('background-theme', backgroundId);

    // Also store in the mode-specific key for current theme
    if (actualTheme === 'dark') {
      localStorage.setItem('background-theme-dark', backgroundId);
    } else {
      localStorage.setItem('background-theme-light', backgroundId);
    }

    // Notify other components
    window.dispatchEvent(new CustomEvent('backgroundThemeChanged', {
      detail: { backgroundId }
    }));

    console.log('Background theme set:', backgroundId);
  };

  // Don't render children until mounted to prevent hydration issues
  if (!mounted) {
    return (
      <div className="fixed inset-0 bg-white dark:bg-black flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-white"></div>
      </div>
    );
  }

  return (
    <ThemeContext.Provider 
      value={{ 
        theme, 
        setTheme, 
        actualTheme, 
        mounted, 
        backgroundTheme, 
        setBackgroundTheme 
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
}