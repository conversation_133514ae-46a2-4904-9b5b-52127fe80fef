# Blade Framework Trigger Issue Report

## Issue Summary
We are experiencing a persistent `TRIGGER_REQUIRED` error when using <PERSON>'s mutation system with properly configured triggers. The error occurs during session flush operations and prevents class creation via Blade mutations, while the same operation works perfectly through custom API routes.

## Error Details
```
ClientError: Please define "during" triggers for the provided write queries.
    code: "TRIGGER_REQUIRED"
```

This error occurs specifically when:
1. Using `useMutation` hook with `add.class()` 
2. During Better Auth session flush operations
3. On pages like `/teacher/mark-madsen/classes`

## Current Trigger Configuration

### Class Trigger (`triggers/class.ts`)
```typescript
// triggers/class.ts
import type { AddTrigger, SetTrigger, RemoveTrigger } from 'blade/types';

export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processClassData = (classData: any) => {
    console.log('Class add trigger - processing data:', classData);

    // Set default values
    classData.isActive = classData.isActive ?? true;
    classData.currentEnrollment = classData.currentEnrollment ?? 0;
    classData.maxCapacity = classData.maxCapacity ?? 30;
    classData.createdAt = classData.createdAt || new Date();
    classData.updatedAt = classData.updatedAt || new Date();

    console.log('Class add trigger - processed data:', classData);
    return classData;
  };

  // Handle array of classes
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processClassData);
  } else {
    // Handle single class
    typedQuery.with = processClassData(typedQuery.with);
  }

  return typedQuery;
};

export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  // Update timestamp
  typedQuery.to.updatedAt = new Date();

  console.log('Class set trigger - processed data:', typedQuery.to);
  return typedQuery;
};

export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('Class remove trigger called with query:', typedQuery);
  return typedQuery;
};
```

### Session Trigger (`triggers/session.ts`)
```typescript
// triggers/session.ts
import type { AddTrigger, SetTrigger, RemoveTrigger } from 'blade/types';

export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processSessionData = (sessionData: any) => {
    console.log('Session add trigger - processing data:', sessionData);

    // Set default timestamps
    sessionData.createdAt = new Date();
    sessionData.updatedAt = new Date();

    // Set expiration (30 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30);
    sessionData.expiresAt = expiresAt;

    console.log('Session add trigger - processed data:', sessionData);
    return sessionData;
  };

  // Handle array of sessions
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processSessionData);
  } else {
    // Handle single session
    typedQuery.with = processSessionData(typedQuery.with);
  }

  return typedQuery;
};

export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  // Update timestamp
  typedQuery.to.updatedAt = new Date();

  console.log('Session set trigger - processed data:', typedQuery.to);
  return typedQuery;
};

export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  console.log('Session remove trigger called with query:', typedQuery);
  return typedQuery;
};
```

## Component Using Blade Mutation (FAILS)

### Enhanced Sidebar Component
```typescript
// components/auth/teacher/dual-sidebar/enhanced-sidebar.client.tsx
import { useMutation } from 'blade/client/hooks';

export function EnhancedSidebar() {
  const { add: addMutation } = useMutation();

  const handleAddClass = async (className: string) => {
    try {
      if (!user || user.role !== 'teacher') {
        throw new Error('Only teachers can add classes');
      }

      // Use Blade mutation instead of API route
      const result = await addMutation.class({
        with: {
          name: className,
          description: `${className} class`,
          teacherId: user.id, // The class trigger will convert User ID to Teacher record ID
          maxCapacity: 30
        }
      });

      if (!result.success) {
        throw new Error(result.error || 'Failed to create class');
      }

      console.log('✅ Class created via Blade mutation:', result);
      
    } catch (error) {
      console.error('❌ Error creating class via Blade mutation:', error);
      // This is where the TRIGGER_REQUIRED error occurs
    }
  };
}
```

### Classes page
```typescript
// pages/teacher/[slug]/classes.tsx
import { use, useBatch } from 'blade/server/hooks';
import { useParams } from 'blade/hooks';
import TeacherClassesPageWithData from '../../../components/teacher/TeacherClassesPageWithData.client';

// Define types to fix implicit 'any' errors
interface User {
  id: string;
  name: string;
  email: string;
  slug: string;
  role: string;
  teacherId?: string;
  username?: string;
  classId?: string;
  grade?: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface ClassItem {
  id: string;
  name: string;
  description?: string;
  teacherId: string;
  schoolId?: string;
  subjectId?: string;
  gradeLevel?: string;
  maxCapacity?: number;
  currentEnrollment?: number;
  isActive?: boolean;
  startDate?: string;
  endDate?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface GradeLevel {
  id: string;
  name: string;
  code?: string;
  description?: string;
  category: string;
  educationType: string;
  teacherId: string;
  sortOrder?: number;
  isActive?: boolean;
}

interface EducationalContext {
  id: string;
  name: string;
  teacherId: string;
  isActive?: boolean;
  description?: string;
  createdAt?: string;
}

interface StudentClassAssignment {
  id: string;
  studentId: string;
  classId: string;
  enrolledAt: string;
  status: string;
}

const TeachersClassesPage = () => {
  const { slug } = useParams();

  // OPTIMIZED: Single batched query instead of multiple separate queries
  // This prevents the excessive revalidation and matches the working students page pattern
  const [allUsers, allClasses, allGradeLevels, allEducationalContexts, allStudents, allStudentClassAssignments, allTeachers] = useBatch(() => [
    // Get ALL users (teachers and students) in one query
    use.users({
      where: {
        OR: [
          { role: 'teacher' },
          { role: 'student' }
        ]
      }
    }),

    // Get ALL classes
    use.classes({
      where: { isActive: true }
    }),

    // Get ALL grade levels
    use.gradeLevels({
      where: { isActive: true },
      orderedBy: { ascending: ['sortOrder', 'name'] }
    }),

    // Get ALL educational contexts
    use.educationalContexts({
      where: { isActive: true }
    }),

    // Get ALL student users
    use.users({
      where: { role: 'student' },
      selecting: ['id', 'name', 'email', 'username', 'grade', 'isActive', 'role']
    }),

    // Get ALL student-class assignments
    use.studentClasses({
      selecting: ['id', 'studentId', 'classId', 'enrolledAt', 'status']
    }),

    // Get ALL teacher records (needed for matching)
    use.teachers()
  ]);

  // Find the current teacher user
  const teacher = allUsers.find((u: User) => u.slug === slug && u.role === 'teacher');

  if (!teacher) {
    return null; // Or a loading/error state
  }

  // Find the Teacher record for this user (needed for class relationships)
  const teacherRecord = allTeachers.find((t: any) => t.userId === teacher.id);

  if (!teacherRecord) {
    console.error('Teacher record not found for user:', teacher.id);
    return null;
  }

  // Filter data for this specific teacher using Teacher record ID
  const teacherClasses = allClasses.filter((classItem: ClassItem) =>
    classItem.teacherId === teacherRecord.id && classItem.isActive !== false
  );

  const teacherGradeLevels = allGradeLevels.filter((grade: GradeLevel) =>
    grade.teacherId === teacher.id && grade.isActive !== false
  );

  const teacherEducationalContexts = allEducationalContexts.filter((context: EducationalContext) =>
    context.teacherId === teacher.id && context.isActive !== false
  );

  const teacherStudents = allStudents.filter((student: User) => {
    return student.isActive !== false && student.role === 'student';
  });

  const teacherStudentClassAssignments = allStudentClassAssignments;

  return (
    <TeacherClassesPageWithData
      allClasses={teacherClasses}
      allGradeLevels={teacherGradeLevels}
      allEducationalContexts={teacherEducationalContexts}
      allStudents={teacherStudents}
      allStudentClassAssignments={teacherStudentClassAssignments}
      teacher={teacher}
    />
  );
};

export default TeachersClassesPage;
```

## API Route Alternative (WORKS PERFECTLY)

### Working API Route in router.ts
```typescript
// router.ts - Create class endpoint (bypasses trigger issues)
app.post("/api/create-class", async (c) => {
  try {
    // Get the current session to verify the user is a teacher
    const session = await auth.api.getSession({
      headers: c.req.raw.headers
    });

    if (!session || !session.user) {
      c.header('Content-Type', 'application/json');
      return c.json({ error: 'Unauthorized' }, 401);
    }

    // Check if the user is a teacher or school_admin
    if (session.user.role !== 'teacher' && session.user.role !== 'school_admin') {
      c.header('Content-Type', 'application/json');
      return c.json({ error: 'Insufficient permissions' }, 403);
    }

    // Get the request body
    const body = await c.req.json();
    const { name, description, gradeLevel, maxCapacity } = body;

    console.log('Creating class via API:', { name, description, gradeLevel, maxCapacity, teacherId: session.user.id });

    // Get RONIN instance
    const ronin = c.var.ronin;

    // Get the Teacher record ID (not the User ID) - needed for class creation
    let teacherRecordId;
    try {
      const teacherRecord = await ronin.get.teacher({
        where: { userId: session.user.id }
      });
      teacherRecordId = teacherRecord.id;
      console.log('📝 Found teacher record:', { teacherRecordId, teacherUserId: session.user.id });
    } catch (teacherError) {
      console.error('❌ Failed to find teacher record:', teacherError);
      c.header('Content-Type', 'application/json');
      return c.json({ error: 'Teacher record not found' }, 404);
    }

    // Create the class using RONIN directly
    try {
      console.log('📝 Creating class record:', {
        name,
        description: description || `${name} class`,
        teacherId: teacherRecordId,
        gradeLevel: gradeLevel || '',
        maxCapacity: maxCapacity || 30
      });

      const classRecord = await ronin.add.class({
        with: {
          name: name,
          description: description || `${name} class`,
          teacherId: teacherRecordId, // Use Teacher record ID, not User ID
          gradeLevel: gradeLevel || '',
          maxCapacity: maxCapacity || 30,
          currentEnrollment: 0,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      console.log('✅ Class created successfully:', classRecord);

      c.header('Content-Type', 'application/json');
      return c.json({
        success: true,
        class: {
          id: classRecord.id,
          name: classRecord.name,
          description: classRecord.description,
          teacherId: classRecord.teacherId,
          gradeLevel: classRecord.gradeLevel,
          maxCapacity: classRecord.maxCapacity,
          currentEnrollment: classRecord.currentEnrollment,
          isActive: classRecord.isActive,
          createdAt: classRecord.createdAt,
          updatedAt: classRecord.updatedAt
        }
      });

    } catch (error) {
      console.error('❌ Error creating class:', error);
      c.header('Content-Type', 'application/json');
      return c.json({ error: 'Failed to create class' }, 500);
    }

  } catch (error) {
    console.error('Error in create-class endpoint:', error);
    c.header('Content-Type', 'application/json');
    return c.json({ error: 'Internal server error' }, 500);
  }
});
```

## Key Observations

1. **Trigger Structure**: We've tried both `during` object exports and individual exports (`export const add: AddTrigger`). Based on Blade documentation, individual exports are correct.

2. **Import Path**: Using `import type { AddTrigger, SetTrigger, RemoveTrigger } from 'blade/types';`

3. **Trigger Logic**: All triggers follow the expected pattern of receiving `(query, multiple, options)` and returning the modified query.

4. **API Route Success**: The exact same database operation works perfectly when using custom API routes with `ronin.add.class()`.

5. **Session Integration**: The error seems related to Better Auth session management, as it occurs during "session flush" operations.

## Questions for Blade Team

2. **Trigger Detection**: How does Blade detect and load trigger files? Are there specific naming conventions or export requirements we're missing?

3. **Session Integration**: Why would session flush operations require triggers for class creation? Is there a dependency we're not aware of?

4. **Debugging**: Are there any debugging tools or logs we can enable to see exactly which triggers Blade is looking for?

5. **Alternative Approaches**: Should we be using a different approach for database operations that integrate with Better Auth?

## Environment Details

- **Blade Framework**: Latest version
- **Authentication**: Better Auth integration
- **Database**: RONIN
- **Framework**: React with TypeScript

## Request

We would greatly appreciate guidance on resolving this trigger issue, as we prefer to use Blade's mutation system over custom API routes for consistency and to leverage the framework's built-in features.
