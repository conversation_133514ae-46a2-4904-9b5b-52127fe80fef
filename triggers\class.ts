// triggers/class.ts
import type { Add<PERSON>rigger, SetTrigger, RemoveTrigger } from 'blade/types';

export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processClassData = (classData: any) => {
    console.log('Class add trigger - processing data:', classData);

    // Set default values
    classData.isActive = classData.isActive ?? true;
    classData.currentEnrollment = classData.currentEnrollment ?? 0;
    classData.maxCapacity = classData.maxCapacity ?? 30;
    classData.createdAt = classData.createdAt || new Date();
    classData.updatedAt = classData.updatedAt || new Date();

    console.log('Class add trigger - processed data:', classData);
    return classData;
  };

  // Handle array of classes
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processClassData);
  } else {
    // Handle single class
    typedQuery.with = processClassData(typedQuery.with);
  }

  return typedQuery;
};

export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  // Update timestamp
  typedQuery.to.updatedAt = new Date();

  console.log('Class set trigger - processed data:', typedQuery.to);
  return typedQuery;
};

export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('Class remove trigger called with query:', typedQuery);
  return typedQuery;
};