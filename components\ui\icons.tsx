import React, { useState, useEffect, useRef } from 'react';
import { DateTime } from 'luxon';

// Base icon props interface
interface IconProps extends React.SVGProps<SVGSVGElement> {
  size?: number;
}

// Standardized icon wrapper with consistent sizing
const IconWrapper: React.FC<{ children: React.ReactNode; size?: number; className?: string; viewBox?: string }> = ({ 
  children, 
  size = 24, 
  className = "", 
  viewBox = "0 0 24 24",
  ...props 
}) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    width={size} 
    height={size} 
    viewBox={viewBox}
    className={`flex-shrink-0 ${className}`}
    {...props}
  >
    {children}
  </svg>
);

// FIXED: Dynamic Calendar Icon with date functionality
export const CalendarIcon = ({ size = 24, className = "", ...props }: IconProps) => {
  const [currentDate, setCurrentDate] = useState(() => DateTime.now());
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastDateRef = useRef<string>(DateTime.now().toISODate());

  useEffect(() => {
    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Set up interval to check for date changes
    intervalRef.current = setInterval(() => {
      const now = DateTime.now();
      const newDay = now.toISODate();
      
      // Only update state if the date actually changed
      if (lastDateRef.current !== newDay) {
        lastDateRef.current = newDay;
        setCurrentDate(now);
      }
    }, 60000); // Check every minute

    // Cleanup function
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, []); // Empty dependency array - only run once on mount

  const day = currentDate.day.toString();

  return (
    <IconWrapper size={size} className={className} {...props}>
      <path fill="currentColor" d="M18.435 4.955h-1.94v-1.41c0-.26-.23-.51-.5-.5c-.27.01-.5.22-.5.5v1.41h-7v-1.41c0-.26-.23-.51-.5-.5c-.27.01-.5.22-.5.5v1.41h-1.93a2.5 2.5 0 0 0-2.5 2.5v11a2.5 2.5 0 0 0 2.5 2.5h12.87a2.5 2.5 0 0 0 2.5-2.5v-11a2.5 2.5 0 0 0-2.5-2.5zm1.5 13.5c0 .83-.67 1.5-1.5 1.5H5.565c-.83 0-1.5-.67-1.5-1.5v-8.42h15.87v8.42zm0-9.42H4.065v-1.58c0-.83.67-1.5 1.5-1.5h1.93v.59c0 .26.23.51.5.5c.27-.01.5-.22.5-.5v-.59h7v.59c0 .26.23.51.5.5c.27-.01.5-.22.5-.5v-.59h1.94c.83 0 1.5.67 1.5 1.5v1.58z"/>      
      {/* Day number positioned in the calendar */}
      <text
        x="12"
        y="16.5"
        textAnchor="middle"
        className="fill-current text-[8px] font-medium"
        style={{ 
          fontFamily: 'system-ui, -apple-system, sans-serif',
          fontSize: '8px',
          fontWeight: '500'
        }}
      >
        {day}
      </text>
    </IconWrapper>
  );
};

// Alternative simpler solution - just update when component mounts
export const CalendarIconSimple = ({ size = 24, className = "", ...props }: IconProps) => {
  const [currentDate] = useState(() => DateTime.now());
  const day = currentDate.day.toString();

  return (
    <IconWrapper size={size} className={className} {...props}>
      <path fill="currentColor" d="M18.435 4.955h-1.94v-1.41c0-.26-.23-.51-.5-.5c-.27.01-.5.22-.5.5v1.41h-7v-1.41c0-.26-.23-.51-.5-.5c-.27.01-.5.22-.5.5v1.41h-1.93a2.5 2.5 0 0 0-2.5 2.5v11a2.5 2.5 0 0 0 2.5 2.5h12.87a2.5 2.5 0 0 0 2.5-2.5v-11a2.5 2.5 0 0 0-2.5-2.5zm1.5 13.5c0 .83-.67 1.5-1.5 1.5H5.565c-.83 0-1.5-.67-1.5-1.5v-8.42h15.87v8.42zm0-9.42H4.065v-1.58c0-.83.67-1.5 1.5-1.5h1.93v.59c0 .26.23.51.5.5c.27-.01.5-.22.5-.5v-.59h7v.59c0 .26.23.51.5.5c.27-.01.5-.22.5-.5v-.59h1.94c.83 0 1.5.67 1.5 1.5v1.58z"/>      
      <text
        x="12"
        y="16.5"
        textAnchor="middle"
        className="fill-current text-[8px] font-medium"
        style={{ 
          fontFamily: 'system-ui, -apple-system, sans-serif',
          fontSize: '8px',
          fontWeight: '500'
        }}
      >
        {day}
      </text>
    </IconWrapper>
  );
};

// All your other icons remain the same...
export const TeacherIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
    <path fill="currentColor" d="M5 5h13a3 3 0 0 1 3 3v9a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3V8a3 3 0 0 1 3-3m0 1c-.5 0-.94.17-1.28.47l7.78 5.03l7.78-5.03C18.94 6.17 18.5 6 18 6H5m6.5 6.71L3.13 7.28C3.05 7.5 3 7.75 3 8v9a2 2 0 0 0 2 2h13a2 2 0 0 0 2-2V8c0-.25-.05-.5-.13-.72l-8.37 5.43Z"/>
  </IconWrapper>
);

export const SchoolIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
    <path fill="currentColor" d="M11 2.5L20 7v2H2V7l9-4.5m4 7.5h4v8h-4v-8M2 22v-3h18v3H2m7-12h4v8H9v-8m-6 0h4v8H3v-8m0 10v1h16v-1H3m1-9v6h2v-6H4m6 0v6h2v-6h-2m6 0v6h2v-6h-2M3 8h16v-.4l-8-4.02L3 7.6V8Z"/>
  </IconWrapper>
);


export const SearchIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
    <path fill="currentColor" d="M9.5 4a6.5 6.5 0 0 1 6.5 6.5c0 1.62-.59 3.1-1.57 4.23l5.65 5.65l-.71.71l-5.65-5.65A6.469 6.469 0 0 1 9.5 17A6.5 6.5 0 0 1 3 10.5A6.5 6.5 0 0 1 9.5 4m0 1A5.5 5.5 0 0 0 4 10.5A5.5 5.5 0 0 0 9.5 16a5.5 5.5 0 0 0 5.5-5.5A5.5 5.5 0 0 0 9.5 5Z"/>
  </IconWrapper>
);

export const CollapseBarIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
    <path d="M2.75 6.5C2.75 4.42893 4.42893 2.75 6.5 2.75H17.5C19.5711 2.75 21.25 4.42893 21.25 6.5V17.5C21.25 19.5711 19.5711 21.25 17.5 21.25H6.5C4.42893 21.25 2.75 19.5711 2.75 17.5V6.5ZM6.5 4.25C5.25736 4.25 4.25 5.25736 4.25 6.5V17.5C4.25 18.7426 5.25736 19.75 6.5 19.75H8.25V4.25H6.5ZM17.5 19.75H9.75V4.25H17.5C18.7426 4.25 19.75 5.25736 19.75 6.5V17.5C19.75 18.7426 18.7426 19.75 17.5 19.75Z" fill="currentColor"/>
  </IconWrapper>
);

export const HouseIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
    <path fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M17 21H7a4 4 0 0 1-4-4v-6.292a4 4 0 0 1 1.927-3.421l5-3.03a4 4 0 0 1 4.146 0l5 3.03A4 4 0 0 1 21 10.707V17a4 4 0 0 1-4 4Zm-8-4h6"/>
  </IconWrapper>
);

export const StudentIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
    <path d="M11.1093 1.90438C11.6584 1.58014 12.3406 1.57996 12.8896 1.90438L21.9238 7.24324C22.5024 7.58523 22.7878 8.17711 22.7822 8.76668V19.0313C22.7822 19.4455 22.4464 19.7813 22.0322 19.7813C21.618 19.7813 21.2822 19.4455 21.2822 19.0313V10.6348L19.749 11.5411V16.7247C19.749 16.8885 19.7168 17.0725 19.6269 17.252C19.2683 17.9685 17.3517 21.1495 11.999 21.1495C6.64628 21.1495 4.72971 17.9685 4.37106 17.252C4.28121 17.0725 4.24899 16.8885 4.24899 16.7247V11.5401L2.07419 10.2559C0.928423 9.57849 0.928368 7.92065 2.07419 7.24324L11.1093 1.90438ZM12.8896 15.5948C12.3406 15.9192 11.6584 15.9191 11.1093 15.5948L5.74899 12.4268V16.6505C6.05691 17.2212 7.61288 19.6495 11.999 19.6495C16.3851 19.6495 17.9411 17.2212 18.249 16.6505V12.4268L12.8896 15.5948ZM12.1259 3.19637C12.0475 3.15007 11.9505 3.15003 11.872 3.19637L2.83786 8.53426C2.67411 8.63102 2.6741 8.86816 2.83786 8.96492L11.872 14.3038C11.9503 14.3499 12.0477 14.3498 12.1259 14.3038L21.1611 8.96492C21.2404 8.91792 21.2799 8.83777 21.2822 8.75692V8.75008H21.2832C21.2833 8.66661 21.2429 8.58275 21.1611 8.53426L12.1259 3.19637Z" fill="currentColor"/>
  </IconWrapper>
);

export const StatusIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
    <path fillRule="evenodd" clipRule="evenodd" d="M12 3.75C7.44365 3.75 3.75 7.44365 3.75 12C3.75 16.5563 7.44365 20.25 12 20.25C16.5563 20.25 20.25 16.5563 20.25 12C20.25 7.44365 16.5563 3.75 12 3.75ZM2.25 12C2.25 6.61522 6.61522 2.25 12 2.25C17.3848 2.25 21.75 6.61522 21.75 12C21.75 17.3848 17.3848 21.75 12 21.75C6.61522 21.75 2.25 17.3848 2.25 12Z" fill="currentColor"/>
    <path d="M12.5 18.4811C15.8562 18.2257 18.5 15.4216 18.5 12C18.5 8.57838 15.8562 5.77425 12.5 5.51894C12.2246 5.498 12 5.72386 12 6V18C12 18.2761 12.2246 18.502 12.5 18.4811Z" fill="currentColor"/>
  </IconWrapper>
);

export const UserIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
   <path fill-rule="evenodd" clip-rule="evenodd" d="M12 3.75C10.2051 3.75 8.75 5.20507 8.75 7C8.75 8.79493 10.2051 10.25 12 10.25C13.7949 10.25 15.25 8.79493 15.25 7C15.25 5.20507 13.7949 3.75 12 3.75ZM7.25 7C7.25 4.37665 9.37665 2.25 12 2.25C14.6234 2.25 16.75 4.37665 16.75 7C16.75 9.62335 14.6234 11.75 12 11.75C9.37665 11.75 7.25 9.62335 7.25 7Z" fill="currentColor"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M3.25 19.6C3.25 16.093 6.09299 13.25 9.6 13.25H14.4C17.907 13.25 20.75 16.093 20.75 19.6C20.75 20.7874 19.7874 21.75 18.6 21.75H5.4C4.21259 21.75 3.25 20.7874 3.25 19.6ZM9.6 14.75C6.92142 14.75 4.75 16.9214 4.75 19.6C4.75 19.959 5.04101 20.25 5.4 20.25H18.6C18.959 20.25 19.25 19.959 19.25 19.6C19.25 16.9214 17.0786 14.75 14.4 14.75H9.6Z" fill="currentColor"/>

  </IconWrapper>
);


export const EmailIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
<path fill-rule="evenodd" clip-rule="evenodd" d="M4.5 4.25C2.70507 4.25 1.25 5.70507 1.25 7.5V16.5C1.25 18.2949 2.70507 19.75 4.5 19.75H19.5C21.2949 19.75 22.75 18.2949 22.75 16.5V7.5C22.75 5.70507 21.2949 4.25 19.5 4.25H4.5ZM3.9477 5.83894C4.12132 5.78124 4.30701 5.75 4.5 5.75H19.5C19.693 5.75 19.8787 5.78124 20.0523 5.83894L12.1661 12.8489C12.0714 12.9331 11.9286 12.9331 11.8339 12.8489L3.9477 5.83894ZM2.86252 6.88127C2.7898 7.07363 2.75 7.28217 2.75 7.5V16.5C2.75 17.4665 3.5335 18.25 4.5 18.25H19.5C20.4665 18.25 21.25 17.4665 21.25 16.5V7.5C21.25 7.28217 21.2102 7.07364 21.1375 6.88128L13.1626 13.97C12.4996 14.5594 11.5004 14.5594 10.8374 13.97L2.86252 6.88127Z" fill="currentColor"/>

  </IconWrapper>
);


export const RoleIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M12 0.708984L19.8696 3.33219C20.9926 3.70651 21.75 4.75739 21.75 5.94107V9.49955C21.75 18.8839 14.2222 22.4212 12.5865 23.0796C12.2061 23.2327 11.7939 23.2327 11.4135 23.0796C9.7778 22.4212 2.25 18.8839 2.25 9.49955V5.94107C2.25 4.75739 3.00743 3.70651 4.13037 3.33219L12 0.708984ZM12 2.29012L4.60472 4.75522C4.09429 4.92536 3.75 5.40303 3.75 5.94107V9.49955C3.75 17.901 10.4572 21.0778 11.9735 21.6881C11.9881 21.6939 11.9963 21.6944 12 21.6944C12.0037 21.6944 12.0119 21.6939 12.0265 21.6881C13.5428 21.0778 20.25 17.901 20.25 9.49955V5.94107C20.25 5.40303 19.9057 4.92536 19.3953 4.75522L12 2.29012Z" fill="currentColor"/>

  </IconWrapper>
);


export const ColorThemeIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
<path fill="currentColor" fill-rule="evenodd" d="M4 2a2 2 0 0 0-2 2v11a3 3 0 1 0 6 0V4a2 2 0 0 0-2-2H4Zm1 14a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm5-1.757l4.9-4.9a2 2 0 0 0 0-2.828L13.485 5.1a2 2 0 0 0-2.828 0L10 5.757v8.486ZM16 18H9.071l6-6H16a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2Z" clip-rule="evenodd"/>

  </IconWrapper>
);


export const SignoutIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.5 4.75C4.80964 4.75 4.25 5.30964 4.25 6V18C4.25 18.6904 4.80964 19.25 5.5 19.25H11.5C11.9142 19.25 12.25 19.5858 12.25 20C12.25 20.4142 11.9142 20.75 11.5 20.75H5.5C3.98122 20.75 2.75 19.5188 2.75 18V6C2.75 4.48122 3.98122 3.25 5.5 3.25H11.5C11.9142 3.25 12.25 3.58579 12.25 4C12.25 4.41421 11.9142 4.75 11.5 4.75H5.5Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M15.5715 7.49392C15.851 7.18822 16.3254 7.16698 16.6311 7.44648L21.0061 11.4465C21.1615 11.5886 21.25 11.7894 21.25 12C21.25 12.2106 21.1615 12.4114 21.0061 12.5535L16.6311 16.5535C16.3254 16.833 15.851 16.8118 15.5715 16.5061C15.292 16.2004 15.3132 15.726 15.6189 15.4465L18.5682 12.75H10.5C10.0858 12.75 9.75 12.4142 9.75 12C9.75 11.5858 10.0858 11.25 10.5 11.25H18.5682L15.6189 8.55352C15.3132 8.27402 15.292 7.79962 15.5715 7.49392Z" fill="currentColor"/>

  </IconWrapper>
);

export const CheckIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
<path fill-rule="evenodd" clip-rule="evenodd" d="M21.5384 5.47789C21.8268 5.77526 21.8195 6.25007 21.5221 6.53843L9.49518 18.2009C9.01032 18.6711 8.23968 18.6711 7.75482 18.2009L2.47789 13.0839C2.18053 12.7955 2.17322 12.3207 2.46158 12.0233C2.74993 11.726 3.22475 11.7187 3.52211 12.007L8.625 16.9553L20.4779 5.46158C20.7753 5.17322 21.2501 5.18053 21.5384 5.47789Z" fill="currentColor"/>

  </IconWrapper>
);


export const CloseIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
<path d="M18.1878 17.1262C18.4807 17.419 18.4807 17.8939 18.1878 18.1868C17.8949 18.4797 17.42 18.4797 17.1271 18.1868L12.0008 13.0605L6.87454 18.1868C6.58164 18.4797 6.10677 18.4797 5.81388 18.1868C5.52098 17.8939 5.52098 17.419 5.81388 17.1262L10.9402 11.9999L5.81342 6.8731C5.52053 6.58021 5.52053 6.10534 5.81342 5.81244C6.10631 5.51955 6.58119 5.51955 6.87408 5.81244L12.0008 10.9392L17.1276 5.81244C17.4205 5.51955 17.8954 5.51955 18.1882 5.81244C18.4811 6.10534 18.4811 6.58021 18.1882 6.8731L13.0615 11.9999L18.1878 17.1262Z" fill="currentColor"/>

  </IconWrapper>
);

export const SunIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 8.75C10.2051 8.75 8.75 10.2051 8.75 12C8.75 13.7949 10.2051 15.25 12 15.25C13.7949 15.25 15.25 13.7949 15.25 12C15.25 10.2051 13.7949 8.75 12 8.75ZM7.25 12C7.25 9.37665 9.37665 7.25 12 7.25C14.6234 7.25 16.75 9.37665 16.75 12C16.75 14.6234 14.6234 16.75 12 16.75C9.37665 16.75 7.25 14.6234 7.25 12Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M22.75 12C22.75 12.4142 22.4142 12.75 22 12.75H19.5C19.0858 12.75 18.75 12.4142 18.75 12C18.75 11.5858 19.0858 11.25 19.5 11.25H22C22.4142 11.25 22.75 11.5858 22.75 12Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 22.75C11.5858 22.75 11.25 22.4142 11.25 22V19.5C11.25 19.0858 11.5858 18.75 12 18.75C12.4142 18.75 12.75 19.0858 12.75 19.5V22C12.75 22.4142 12.4142 22.75 12 22.75Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M4.3986 4.3986C4.6915 4.10571 5.16637 4.10571 5.45926 4.3986L7.22703 6.16637C7.51992 6.45926 7.51992 6.93414 7.22703 7.22703C6.93414 7.51992 6.45926 7.51992 6.16637 7.22703L4.3986 5.45926C4.10571 5.16637 4.10571 4.6915 4.3986 4.3986Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M19.6014 4.39861C19.8943 4.6915 19.8943 5.16638 19.6014 5.45927L17.8336 7.22704C17.5407 7.51993 17.0658 7.51993 16.7729 7.22704C16.4801 6.93414 16.4801 6.45927 16.7729 6.16638L18.5407 4.39861C18.8336 4.10572 19.3085 4.10572 19.6014 4.39861Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.25 12C5.25 12.4142 4.91421 12.75 4.5 12.75H2C1.58579 12.75 1.25 12.4142 1.25 12C1.25 11.5858 1.58579 11.25 2 11.25H4.5C4.91421 11.25 5.25 11.5858 5.25 12Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 5.25C11.5858 5.25 11.25 4.91421 11.25 4.5V2C11.25 1.58579 11.5858 1.25 12 1.25C12.4142 1.25 12.75 1.58579 12.75 2V4.5C12.75 4.91421 12.4142 5.25 12 5.25Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.773 16.773C17.0659 16.4801 17.5407 16.4801 17.8336 16.773L19.6014 18.5407C19.8943 18.8336 19.8943 19.3085 19.6014 19.6014C19.3085 19.8943 18.8336 19.8943 18.5407 19.6014L16.773 17.8336C16.4801 17.5407 16.4801 17.0659 16.773 16.773Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.22705 16.773C7.51994 17.0659 7.51994 17.5408 7.22705 17.8337L5.45928 19.6014C5.16639 19.8943 4.69152 19.8943 4.39862 19.6014C4.10573 19.3086 4.10573 18.8337 4.39862 18.5408L6.16639 16.773C6.45928 16.4801 6.93416 16.4801 7.22705 16.773Z" fill="currentColor"/>

  </IconWrapper>
);

export const MoonIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.9267 3.31517C6.60051 3.84432 3.25 7.53088 3.25 12C3.25 16.8325 7.16751 20.75 12 20.75C16.4692 20.75 20.1558 17.3993 20.6849 13.073C19.7714 13.4762 18.7611 13.7 17.6998 13.7C13.6129 13.7 10.2998 10.3869 10.2998 6.3C10.2998 5.23876 10.5236 4.22861 10.9267 3.31517ZM1.75 12C1.75 6.33908 6.33908 1.75 12 1.75C12.7367 1.75 13.092 2.55706 12.7366 3.1084C12.1438 4.02807 11.7998 5.12271 11.7998 6.3C11.7998 9.55848 14.4413 12.2 17.6998 12.2C18.8771 12.2 19.9717 11.856 20.8913 11.2632C21.4427 10.9078 22.25 11.2631 22.25 12C22.25 17.6609 17.6609 22.25 12 22.25C6.33908 22.25 1.75 17.6609 1.75 12Z" fill="currentColor"/>

  </IconWrapper>
);


















