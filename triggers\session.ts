// triggers/session.ts
import type { Add<PERSON>rigger, SetTrigger, RemoveTrigger } from 'blade/types';

export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processSessionData = (sessionData: any) => {
    console.log('Session add trigger - processing data:', sessionData);

    // Set default timestamps
    sessionData.createdAt = new Date();
    sessionData.updatedAt = new Date();

    // Set default expiration (24 hours from now)
    if (!sessionData.expiresAt) {
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);
      sessionData.expiresAt = expiresAt;
    }

    console.log('Session add trigger - processed data:', sessionData);
    return sessionData;
  };

  // Handle array of sessions
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processSessionData);
  } else {
    // Handle single session
    typedQuery.with = processSessionData(typedQuery.with);
  }

  return typedQuery;
};

export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  // Update timestamp
  typedQuery.to.updatedAt = new Date();

  console.log('Session set trigger - processed data:', typedQuery.to);
  return typedQuery;
};

export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any cleanup logic here if needed
  console.log('Session remove trigger called with query:', typedQuery);
  return typedQuery;
};