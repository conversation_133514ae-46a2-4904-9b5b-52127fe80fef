'use client';

import React, { useState } from 'react';
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionPanel,
} from '../../../animate-ui/base/accordion';
import { School, UserPlus } from 'lucide-react';
import ClassAssignmentDialog from '../dialogs/class-assignment-dialog.client';

interface ClassItem {
  id: string;
  name: string;
  description?: string;
  teacherId: string;
  schoolId?: string;
  subjectId?: string;
  gradeLevel?: string;
  maxCapacity?: number;
  currentEnrollment?: number;
  isActive?: boolean;
  startDate?: string;
  endDate?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface GradeLevel {
  id: string;
  name: string;
  code?: string;
  description?: string;
  category: string;
  educationType: string;
  teacherId: string;
  schoolId?: string;
  sortOrder?: number;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface EducationalContext {
  id: string;
  name: string;
  type: string;
  description?: string;
  defaultGradeLevels?: string;
  teacherId: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface Teacher {
  id: string;
  name: string;
  email: string;
  slug: string;
}

interface ClassManagementTabsWithDataProps {
  classes: ClassItem[];
  gradeLevels: GradeLevel[];
  educationalContexts: EducationalContext[];
  teacher: Teacher;
}

interface Student {
  id: string;
  name: string;
  email: string;
  username?: string;
  grade?: string;
  isActive?: boolean;
}

interface StudentClassAssignment {
  id: string;
  studentId: string;
  classId: string;
  enrolledAt: string;
  status: string;
}

export default function ClassManagementTabsWithData({
  classes: initialClasses,
  students,
  studentClassAssignments,
  teacher
}: {
  classes: ClassItem[];
  students: Student[];
  studentClassAssignments: StudentClassAssignment[];
  teacher: Teacher;
}) {
  const [classes, setClasses] = useState<ClassItem[]>(initialClasses || []);
  const [showAssignmentDialog, setShowAssignmentDialog] = useState(false);

  // Listen for class creation events with optimistic updates
  React.useEffect(() => {
    const handleClassCreated = (event: CustomEvent) => {
      const { class: newClass, isOptimistic, replaceOptimisticId } = event.detail;
      console.log('📢 ClassManagementTabsWithData received classCreated event:', { newClass, isOptimistic, replaceOptimisticId });

      if (newClass && teacher) {
        if (isOptimistic) {
          // Add optimistic class immediately
          setClasses(prev => {
            const updated = [...prev, newClass].sort((a, b) =>
              (a.name || '').localeCompare(b.name || '')
            );
            console.log('🚀 Added optimistic class to UI:', newClass.name, 'Total classes:', updated.length);
            return updated;
          });
        } else if (replaceOptimisticId) {
          // Replace optimistic class with real one
          setClasses(prev => {
            const updated = prev.map(c => c.id === replaceOptimisticId ? newClass : c)
              .sort((a, b) => (a.name || '').localeCompare(b.name || ''));
            console.log('✅ Replaced optimistic class with real data:', newClass.name, 'Total classes:', updated.length);
            return updated;
          });
        } else {
          // Regular class creation (fallback)
          setClasses(prev => {
            const updated = [...prev, newClass].sort((a, b) =>
              (a.name || '').localeCompare(b.name || '')
            );
            console.log('📝 Added class via fallback:', newClass.name, 'Total classes:', updated.length);
            return updated;
          });
        }
      } else {
        console.log('❌ Class creation event ignored:', { hasNewClass: !!newClass, hasTeacher: !!teacher });
      }
    };

    const handleClassCreationError = (event: CustomEvent) => {
      const { removeOptimisticId, error } = event.detail;

      if (removeOptimisticId) {
        // Remove failed optimistic update
        setClasses(prev => prev.filter(c => c.id !== removeOptimisticId));
        console.log('❌ Removed failed optimistic class:', error);
      }
    };

    window.addEventListener('classCreated', handleClassCreated as EventListener);
    window.addEventListener('classCreationError', handleClassCreationError as EventListener);

    return () => {
      window.removeEventListener('classCreated', handleClassCreated as EventListener);
      window.removeEventListener('classCreationError', handleClassCreationError as EventListener);
    };
  }, [teacher]);

  // Remove mock data - will be passed as props from server-side data fetching

  return (
    <div className="h-full flex flex-col space-y-6 p-6">
      {/* Classes Section */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="text-xs font-manrope_1 font-medium text-black/80 dark:text-white/80">
            Your Classes <span className="text-black dark:text-white">({classes.length})</span>
          </h3>
          {classes.length > 0 && (
            <button
              onClick={() => setShowAssignmentDialog(true)}
              className="flex items-center gap-1 px-3 py-1.5 text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-md border border-blue-200 dark:border-blue-700 font-medium"
            >
              <UserPlus className="w-3 h-3" />
              Assign Students
            </button>
          )}
        </div>

        {classes.length === 0 ? (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
            <School className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <h2 className="text-xl font-semibold mb-4">No Classes Yet</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Create your first class to start organizing your students. You can assign grade levels,
              set up educational contexts, and manage your teaching materials.
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Use the command menu (Ctrl+K) to add your first class.
            </p>
          </div>
        ) : (
          <div className="border border-black/10 dark:border-white/10 rounded-xl overflow-hidden shadow-sm">
            <Accordion className="w-full" defaultValue={classes.map(c => c.id)}>
              {classes.map((classItem) => {
                // Get actual student count from StudentClass relationships
                const studentCount = studentClassAssignments.filter(assignment =>
                  assignment.classId === classItem.id && assignment.status === 'active'
                ).length;

                return (
                  <AccordionItem
                    key={classItem.id}
                    value={classItem.id}
                    className="border-b border-black/10 dark:border-white/10 last:border-b-0"
                  >
                    <AccordionTrigger className={`flex items-center justify-between w-full p-4 text-left transition-all duration-200 ${
                      (classItem as any).isOptimistic
                        ? 'bg-blue-50/50 dark:bg-blue-950/10 animate-pulse'
                        : 'hover:bg-gradient-to-r hover:from-blue-50 hover:via-blue-50 hover:to-blue-100 dark:hover:bg-gradient-to-r dark:hover:from-blue-950/20 dark:hover:via-blue-950/30 dark:hover:to-blue-950/20'
                    }`}>
                      <div className="flex items-center gap-3 flex-1">
                        {/* Class icon */}
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                            <School className="w-5 h-5 text-white" />
                          </div>
                        </div>

                        {/* Class info */}
                        <div className="flex-1 min-w-0 font-manrope_1">
                          <div className="font-medium text-sm text-black/90 dark:text-white/90">
                            {classItem.name}
                            {(classItem as any).isOptimistic && (
                              <span className="ml-2 text-xs text-blue-600 dark:text-blue-400 opacity-75">
                                Creating...
                              </span>
                            )}
                          </div>
                          {classItem.description && (
                            <div className="text-xs text-black/60 dark:text-white/60 truncate">{classItem.description}</div>
                          )}
                          <div className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                            {studentCount} {studentCount === 1 ? 'student' : 'students'} enrolled
                          </div>
                        </div>

                        {/* Capacity info */}
                        <div className="flex-shrink-0">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-manrope_1 border border-blue-200 dark:border-blue-700 text-blue-800 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30">
                            {classItem.currentEnrollment || 0}/{classItem.maxCapacity || 30}
                          </span>
                        </div>
                      </div>
                    </AccordionTrigger>

                    <AccordionPanel className="px-4 pb-4">
                      <div className="space-y-3 pt-2">
                        <div className="grid grid-cols-2 gap-4 text-xs">
                          <div>
                            <span className="text-black/60 dark:text-white/60">Max Capacity:</span>
                            <span className="ml-2 text-black/90 dark:text-white/90">{classItem.maxCapacity || 30}</span>
                          </div>
                          <div>
                            <span className="text-black/60 dark:text-white/60">Status:</span>
                            <span className={`ml-2 ${classItem.isActive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                              {classItem.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                        </div>

                        {classItem.description && (
                          <div className="text-xs text-black/70 dark:text-white/70">
                            {classItem.description}
                          </div>
                        )}
                      </div>
                    </AccordionPanel>
                  </AccordionItem>
                );
              })}
            </Accordion>
          </div>
        )}
      </div>

      {/* Class Assignment Dialog */}
      <ClassAssignmentDialog
        isOpen={showAssignmentDialog}
        onClose={() => setShowAssignmentDialog(false)}
        classes={classes}
        students={students}
        studentClassAssignments={studentClassAssignments}
        teacherId={teacher.id}
      />
    </div>
  );
}

// Old tab components removed - now using accordion layout
