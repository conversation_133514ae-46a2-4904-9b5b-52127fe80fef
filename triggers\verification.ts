// triggers/verification.ts
import type { AddTrigger, SetTrigger, RemoveTrigger } from 'blade/types';

export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processVerificationData = (verificationData: any) => {
      console.log('Verification during.add trigger - processing data:', verificationData);

      // Set default expiration if not provided (1 hour from now)
      if (!verificationData.expiresAt) {
        const expiresAt = new Date();
        expiresAt.setHours(expiresAt.getHours() + 1);
        verificationData.expiresAt = expiresAt;
      }

      console.log('Verification during.add trigger - processed data:', verificationData);
      return verificationData;
    };

    // Handle array of verifications
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processVerificationData);
    } else {
      // Handle single verification
      typedQuery.with = processVerificationData(typedQuery.with);
    }

    return typedQuery;
};

export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  console.log('Verification set trigger - processed data:', typedQuery.to);
  return typedQuery;
};

export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('Verification remove trigger called with query:', typedQuery);
  return typedQuery;
};

// Note: Individual trigger exports removed to avoid conflicts with 'during' triggers
// All logic is now in the 'during' object above

/*
// REMOVED: Individual trigger exports to prevent conflicts
export const add = (...args: any[]) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processVerificationData = (verificationData: any) => {
    console.log('Verification creation trigger - processing data:', verificationData);

    // Set default expiration if not provided (1 hour from now)
    if (!verificationData.expiresAt) {
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 1);
      verificationData.expiresAt = expiresAt;
    }

    console.log('Verification trigger - processed verification data:', verificationData);
    return verificationData;
  };

  // Handle array of verifications
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processVerificationData);
  } else {
    // Handle single verification
    typedQuery.with = processVerificationData(typedQuery.with);
  }

  return typedQuery;
};

// Trigger for updating verifications
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  console.log('Verification update trigger - processed data:', typedQuery.to);
  return typedQuery;
};

// Trigger for getting verifications (can be used for access control)
export const get: GetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any access control logic here if needed
  return typedQuery;
};

// Trigger for removing verifications
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('Verification removal trigger called with query:', typedQuery);
  return typedQuery;
};
*/
