﻿// triggers/student.ts
import type { AddTrigger, SetTrigger, RemoveTrigger } from 'blade/types';
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  
    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processStudentData = (studentData: any) => {
      console.log('Student during.add trigger - processing data:', studentData);

      // Set default values
      studentData.grade = studentData.grade || '';
      studentData.createdAt = studentData.createdAt || new Date();
      studentData.updatedAt = studentData.updatedAt || new Date();

      console.log('Student during.add trigger - processed data:', studentData);
      return studentData;
    };

    // Handle array of students
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processStudentData);
    } else {
      // Handle single student
      typedQuery.with = processStudentData(typedQuery.with);
    }

    return typedQuery;
};
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;



    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // Update timestamp
    typedQuery.to.updatedAt = new Date();

    console.log('Student during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
};
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;



    // Add any validation or cleanup logic for deletions
    console.log('Student during.remove trigger called with query:', typedQuery);
    return typedQuery;
};
