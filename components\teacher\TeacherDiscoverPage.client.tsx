// components/teacher/TeacherDiscoverPage.client.tsx
'use client';

import { useParams } from 'blade/hooks';
import { useAuth } from '../../hooks/useAuth';

const TeacherDiscoverPage = () => {
  const { slug } = useParams();
  const { user } = useAuth();
  
  // Verify the slug matches the current user
  if (user && user.slug !== slug) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-gray-600">You can only access your own profile.</p>
        </div>
      </div>
    );
  }


  return (
    <div className="p-2">
      <h1 className="text-3xl font-bold mb-6">Discover</h1>
      <div className="space-y-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
          <h2 className="text-xl font-semibold mb-4">Educational Resources</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Explore new teaching materials, lesson plans, and educational tools to enhance your classroom experience.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <h3 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">Lesson Plans</h3>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Ready-to-use lesson plans for various subjects and grade levels.
              </p>
            </div>
            
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
              <h3 className="font-semibold text-green-800 dark:text-green-200 mb-2">Interactive Tools</h3>
              <p className="text-sm text-green-700 dark:text-green-300">
                Digital tools and apps to make learning more engaging for students.
              </p>
            </div>
            
            <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
              <h3 className="font-semibold text-purple-800 dark:text-purple-200 mb-2">Assessment Tools</h3>
              <p className="text-sm text-purple-700 dark:text-purple-300">
                Modern assessment methods and rubrics for effective evaluation.
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
          <h2 className="text-xl font-semibold mb-4">Professional Development</h2>
          <div className="space-y-4">
            <div className="border-l-4 border-orange-500 pl-4">
              <h3 className="font-semibold">Upcoming Webinars</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Join live sessions with education experts and fellow teachers.
              </p>
            </div>
            
            <div className="border-l-4 border-teal-500 pl-4">
              <h3 className="font-semibold">Certification Courses</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Advance your career with recognized teaching certifications.
              </p>
            </div>
            
            <div className="border-l-4 border-pink-500 pl-4">
              <h3 className="font-semibold">Teacher Community</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Connect with other educators and share best practices.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeacherDiscoverPage;
