# PowerShell script to fix all trigger files by removing individual exports
# This fixes the conflict between 'during' triggers and individual trigger exports

$triggerFiles = Get-ChildItem -Path "triggers" -Filter "*.ts" | Where-Object { $_.Name -ne "index.ts" }

foreach ($file in $triggerFiles) {
    $filePath = $file.FullName
    $fileName = $file.Name
    
    Write-Host "Processing $fileName..."
    
    # Read the file content
    $content = Get-Content -Path $filePath -Raw
    
    # Skip if already processed (contains "REMOVED: Individual trigger exports")
    if ($content -match "REMOVED: Individual trigger exports") {
        Write-Host "  Already processed, skipping..."
        continue
    }
    
    # Remove individual trigger type imports
    $content = $content -replace "import type \{ Add<PERSON>rigger, <PERSON><PERSON>rigger, GetTrigger, RemoveTrigger \} from 'blade/types';", "// Note: Individual trigger type imports removed since we're using 'during' object approach"
    $content = $content -replace "import type \{ AddTrigger, SetTrigger, RemoveTrigger \} from 'blade/types';", "// Note: Individual trigger type imports removed since we're using 'during' object approach"
    
    # Find the start of individual exports and comment them out
    $lines = $content -split "`r?`n"
    $newLines = @()
    $inCommentBlock = $false
    $foundFirstExport = $false
    
    for ($i = 0; $i -lt $lines.Length; $i++) {
        $line = $lines[$i]
        
        # Check if this is the start of individual exports
        if ($line -match "^// Trigger for (creating|updating|getting|removing)" -and !$foundFirstExport) {
            $newLines += "// Note: Individual trigger exports removed to avoid conflicts with 'during' triggers"
            $newLines += "// All logic is now in the 'during' object above"
            $newLines += ""
            $newLines += "/*"
            $newLines += "// REMOVED: Individual trigger exports to prevent conflicts"
            $inCommentBlock = $true
            $foundFirstExport = $true
        }
        
        # If we're in a comment block, modify export lines
        if ($inCommentBlock -and $line -match "^export const (add|set|get|remove): (Add|Set|Get|Remove)Trigger") {
            $line = $line -replace ": (Add|Set|Get|Remove)Trigger", ""
            $line = $line -replace "\(\.\.\.\w+\)", "(...args: any[])"
        }
        
        $newLines += $line
    }
    
    # Add closing comment if we started a comment block
    if ($inCommentBlock) {
        $newLines += "*/"
    }
    
    # Write the modified content back to the file
    $newContent = $newLines -join "`n"
    Set-Content -Path $filePath -Value $newContent -NoNewline
    
    Write-Host "  Fixed $fileName"
}

Write-Host "All trigger files have been processed!"
