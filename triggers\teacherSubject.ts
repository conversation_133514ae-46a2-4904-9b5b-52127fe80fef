﻿// triggers/teacherSubject.ts
import type { AddTrigger, SetTrigger, RemoveTrigger } from 'blade/types';
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;


    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processTeacherSubjectData = (relationshipData: any) => {
      console.log('TeacherSubject during.add trigger - processing data:', relationshipData);

      // Set default values
      relationshipData.createdAt = relationshipData.createdAt || new Date();

      console.log('TeacherSubject during.add trigger - processed data:', relationshipData);
      return relationshipData;
    };

    // Handle array of relationships
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processTeacherSubjectData);
    } else {
      // Handle single relationship
      typedQuery.with = processTeacherSubjectData(typedQuery.with);
    }

    return typedQuery;
};
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;



    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    console.log('TeacherSubject during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
};
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;



    // Add any validation or cleanup logic for deletions
    console.log('TeacherSubject during.remove trigger called with query:', typedQuery);
    return typedQuery;
};
