'use client';

import React from 'react';
import { SettingsSection, NotificationsSection, LogoutSection, ProfileManagement } from './index';
import { CollapseBarIcon } from '../../../ui/icons';
import { cn } from '../../../../lib/utils';

interface UserNavContentProps {
  setFlyout: (flyout: string | null) => void;
}

// Custom FlyoutTrigger that closes the flyout
const FlyoutTrigger = ({
  className,
  setFlyout
}: {
  className?: string;
  setFlyout: (flyout: string | null) => void;
}) => {
  return (
    <button
      onClick={() => setFlyout(null)} // Close flyout
      className={cn(
        "inline-flex items-center justify-center gap-2 w-4 h-4 text-black/60 dark:text-white/60 hover:text-black/90 dark:hover:text-white/90",
        className
      )}
    >
     <CollapseBarIcon/>
    </button>
  );
};

export function UserNavContent({ setFlyout }: UserNavContentProps) {
  return (
    <div className="flex py-2 flex-col h-full">
    

      {/* Content */}
      <div className="flex-1  overflow-auto">
        <div className="space-y-2">
          <ProfileManagement/>
          <SettingsSection />
          <NotificationsSection />
          <LogoutSection onLogout={() => setFlyout(null)} />
        </div>
      </div>
    </div>
  );
}