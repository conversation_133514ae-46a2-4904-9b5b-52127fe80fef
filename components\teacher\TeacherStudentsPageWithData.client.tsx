// components/teacher/TeacherStudentsPageWithData.client.tsx
'use client';

import { useMemo } from 'react';
import { useParams } from 'blade/hooks';
import { TeacherAuthGuard } from '../auth/AuthGuard.client';
import StudentManagementTabsWithData from '../auth/teacher/students/student-management-tabs-with-data.client';
import NoiseText from '../home/<USER>';

interface User {
  id: string;
  name: string;
  email: string;
  slug: string;
  role: string;
  teacherId?: string;
  username?: string;
  classId?: string;
  grade?: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface GradeLevel {
  id: string;
  name: string;
  code?: string;
  description?: string;
  category: string;
  educationType: string;
  teacherId: string;
  sortOrder?: number;
  isActive?: boolean;
}

interface ClassItem {
  id: string;
  name: string;
  description?: string;
  teacherId: string;
  schoolId?: string;
  subjectId?: string;
  gradeLevel?: string;
  maxCapacity?: number;
  currentEnrollment?: number;
  isActive?: boolean;
  startDate?: string;
  endDate?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface TeacherStudentsPageWithDataProps {
  teacher: User;
  activeStudents: User[];
  inactiveStudents: User[];
  teacherGradeLevels: GradeLevel[];
  teacherClasses: ClassItem[];
}

interface StudentTeacher {
  id: string;
  studentId: string;
  teacherId: string;
  assignedAt: string;
  status: string;
}

const TeacherStudentsPageWithData = ({ teacher, activeStudents, inactiveStudents, teacherGradeLevels, teacherClasses }: TeacherStudentsPageWithDataProps) => {

  // If teacher not found, return null - AuthGuard will handle instant redirect
  if (!teacher) {
    return null; // Blade's instant redirect will handle this
  }

  // PERFORMANCE FIX: Memoize the StudentManagementTabsWithData component to prevent re-render loops
  const StudentManagementComponent = useMemo(() => (
    <StudentManagementTabsWithData
      students={activeStudents}
      removedStudents={inactiveStudents}
      teacher={teacher}
      availableGradeLevels={teacherGradeLevels}
      teacherClasses={teacherClasses}
    />
  ), [activeStudents, inactiveStudents, teacher, teacherGradeLevels, teacherClasses]);

  return (
    <TeacherAuthGuard>
      <div className="p-2">
        <NoiseText
              text={` Manage your students and their class assignments.`}
              className="font-redaction-normal text-xl md:text-2xl lg:text-2xl mb-2 leading-tight"
            />

        {/* Use the memoized student management component */}
        {StudentManagementComponent}

      </div>
    </TeacherAuthGuard>
  );
};

export default TeacherStudentsPageWithData;
