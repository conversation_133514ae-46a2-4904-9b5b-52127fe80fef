# PowerShell script to fix all trigger files to use correct Blade signature
# This fixes the TRIGGER_REQUIRED error by using the correct parameter signature

$triggerFiles = Get-ChildItem -Path "triggers" -Filter "*.ts" | Where-Object { $_.Name -ne "index.ts" }

Write-Host "Fixing all trigger files to use correct Blade signature..."
Write-Host "================================================================"

foreach ($file in $triggerFiles) {
    $filePath = $file.FullName
    $fileName = $file.Name
    
    Write-Host "  $fileName - Fixing signature..."
    
    # Read the file content
    $content = Get-Content -Path $filePath -Raw
    
    # Skip if already has correct signature (no import and uses correct params)
    if ($content -match "export const add = \(query: any, multiple: any, options: any\)" -and $content -notmatch "import.*blade/types") {
        Write-Host "    ✅ Already has correct signature, skipping..."
        continue
    }
    
    # Remove the import line
    $content = $content -replace "import type \{ Add<PERSON>rigger, SetTrigger, RemoveTrigger \} from 'blade/types';\r?\n", ""
    
    # Fix add trigger signature
    $content = $content -replace "export const add: AddTrigger = \(\.\.\.args\) => \{[\r\n\s]*const \[query, _multiple, _options\] = args;[\r\n\s]*const typedQuery = query as any;", "export const add = (query: any, multiple: any, options: any) => {"
    $content = $content -replace "export const add: AddTrigger = \(query, multiple, options\) => \{", "export const add = (query: any, multiple: any, options: any) => {"
    
    # Fix set trigger signature  
    $content = $content -replace "export const set: SetTrigger = \(\.\.\.args\) => \{[\r\n\s]*const \[query, _multiple, _options\] = args;[\r\n\s]*const typedQuery = query as any;", "export const set = (query: any, multiple: any, options: any) => {"
    $content = $content -replace "export const set: SetTrigger = \(query, multiple, options\) => \{", "export const set = (query: any, multiple: any, options: any) => {"
    
    # Fix remove trigger signature
    $content = $content -replace "export const remove: RemoveTrigger = \(\.\.\.args\) => \{[\r\n\s]*const \[query, _multiple, _options\] = args;[\r\n\s]*const typedQuery = query as any;", "export const remove = (query: any, multiple: any, options: any) => {"
    $content = $content -replace "export const remove: RemoveTrigger = \(query, multiple, options\) => \{", "export const remove = (query: any, multiple: any, options: any) => {"
    
    # Replace typedQuery with query throughout the file
    $content = $content -replace "typedQuery", "query"
    
    # Clean up extra whitespace and formatting issues
    $content = $content -replace "\r?\n\r?\n\r?\n+", "`n`n"
    $content = $content -replace "^\r?\n+", ""
    
    # Write the fixed content back to the file
    Set-Content -Path $filePath -Value $content -Encoding UTF8
    Write-Host "    ✅ Fixed $fileName"
}

Write-Host "`n================================================================"
Write-Host "✅ All trigger files fixed to use correct Blade signature!"
Write-Host "================================================================"
