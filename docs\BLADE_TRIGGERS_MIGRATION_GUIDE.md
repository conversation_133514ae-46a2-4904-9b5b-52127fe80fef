# Blade Triggers Migration Guide

This guide shows how to migrate from custom API routes in `router.ts` to <PERSON>'s trigger system for internal database operations.

## Overview

Your current architecture uses custom API routes for operations like:
- `/api/create-class`
- `/api/create-student` 
- `/api/assign-student-to-class`
- `/api/check-user-exists`

With <PERSON>'s trigger system, you can eliminate most of these API routes and use `useMutation` hooks instead.

## When to Use Each Approach

### ✅ Use Blade Mutations For:
- **Class creation and management** - Pure database operations
- **Student-class assignments** - Relationship management
- **User profile updates** - Internal data updates
- **Data validation and processing** - Handled by triggers
- **Operations that don't need third-party services**

### ⚠️ Keep API Routes For:
- **Student creation with passwords** - Requires Better Auth integration
- **Email sending operations** - External service integration
- **File uploads** - External storage services
- **Third-party API integrations** - OAuth, payment processing, etc.
- **Complex multi-step operations** - That require external services

## Migration Examples

### 1. Class Creation

**Before (API Route):**
```typescript
// In component
const response = await fetch('/api/create-class', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: className,
    description: `${className} class`,
    maxCapacity: 30
  })
});
```

**After (Blade Mutation):**
```typescript
// In component
const { add } = useMutation();

const result = await add.class({
  with: {
    name: className,
    description: `${className} class`,
    teacherId: user.id, // Trigger converts User ID to Teacher record ID
    maxCapacity: 30
  }
});
```

### 2. Student-Class Assignment

**Before (API Route):**
```typescript
const response = await fetch('/api/assign-student-to-class', {
  method: 'POST',
  body: JSON.stringify({ studentId, classId, action: 'add' })
});
```

**After (Blade Mutation):**
```typescript
const { add } = useMutation();

const result = await add.studentClass({
  with: {
    studentId: studentId, // Should be Student record ID
    classId: classId,
    enrolledAt: new Date(),
    status: 'active'
  }
});
```

### 3. User Profile Updates

**Before (API Route):**
```typescript
const response = await fetch('/api/update-user', {
  method: 'POST',
  body: JSON.stringify({ userId, updates })
});
```

**After (Blade Mutation):**
```typescript
const { set } = useMutation();

const result = await set.user({
  with: { id: userId },
  to: updates
});
```

## Trigger System Benefits

### 1. Automatic Business Logic
Your triggers handle:
- **Data validation** - Ensure required fields and formats
- **Default values** - Set timestamps, status fields, etc.
- **Relationship management** - Convert User IDs to Teacher record IDs
- **Related record creation** - Auto-create Student records when Users are created

### 2. Transaction Safety
All Blade mutations run in database transactions, ensuring data consistency.

### 3. Real-time Updates
Blade automatically revalidates data, so your UI stays in sync.

### 4. Type Safety
Full TypeScript support with your RONIN schema.

## Current Trigger Implementation

### Class Trigger (`triggers/class.ts`)
- Converts `teacherId` from User ID to Teacher record ID
- Sets default values (isActive, currentEnrollment, maxCapacity)
- Handles timestamps

### User Trigger (`triggers/user.ts`)
- Generates slugs and usernames for students
- Creates related records (Student, Teacher, SchoolAdmin)
- Handles role-specific logic

### Student Trigger (`triggers/student.ts`)
- Creates StudentTeacher relationships
- Sets default values and timestamps

## Migration Steps

### Step 1: Update Components
Replace `fetch()` calls with `useMutation` hooks:

```typescript
// Add to imports
import { useMutation } from 'blade/client/hooks';

// In component
const { add, set, remove } = useMutation();
```

### Step 2: Update Function Calls
Change from API routes to mutation methods:

```typescript
// Old
await fetch('/api/create-class', { ... });

// New  
await add.class({ with: { ... } });
```

### Step 3: Remove Unused API Routes
Once migrated, remove the corresponding API routes from `router.ts`.

### Step 4: Keep Complex Operations
Keep API routes that involve:
- Better Auth operations (student creation with passwords)
- Email sending
- File uploads
- Third-party integrations

## Example Implementation

See `components/examples/blade-mutation-examples.client.tsx` for working examples of:
- Class creation with Blade mutations
- Student creation patterns
- Student-class assignments
- User profile updates

## Next Steps

1. **Start with simple operations** - Class creation, assignments
2. **Test thoroughly** - Ensure triggers handle all business logic
3. **Migrate incrementally** - One operation at a time
4. **Keep complex operations** - Student creation with passwords should stay as API routes
5. **Clean up router.ts** - Remove unused API routes after migration

This approach will keep your `router.ts` clean and focused on third-party integrations while leveraging Blade's powerful trigger system for internal operations.
