// pages/student/layout.tsx (Server Component - NO hooks allowed)
import { LayoutWrapper } from '../../components/auth/student/dual-sidebar';
import { AttentionProvider } from 'react-attention';

const StudentLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <AttentionProvider>
      <LayoutWrapper
        showUserAvatar={true}
        showTopNav={true}
        enableRightSidebar={false}
      >
        {children}
      </LayoutWrapper>
    </AttentionProvider>
  );
};

export default StudentLayout;