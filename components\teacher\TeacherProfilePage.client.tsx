// components/teacher/TeacherCalendarPage.client.tsx
'use client';

import { useParams } from 'blade/hooks';
import { useAuth } from '../../hooks/useAuth';
import { TeacherAuthGuard } from '../auth/AuthGuard.client';
import NoiseText from '../home/<USER>';
import { ProfileManagement } from '../auth/shared/user-nav-content';

const TeacherProfilePage = () => {
  const { slug } = useParams();
  const { user } = useAuth();
  
  // Verify the slug matches the current user
  if (user && user.slug !== slug) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-gray-600">You can only access your own profile.</p>
        </div>
      </div>
    );
  }

 

return (
    <TeacherAuthGuard>       
       <ProfileManagement/>
    </TeacherAuthGuard>
  );

};


export default TeacherProfilePage;
