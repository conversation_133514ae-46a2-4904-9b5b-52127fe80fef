//app-sidebar.client.tsx
'use client';
import type { ComponentProps } from "react";
import { useCallback, useMemo } from "react";
import { cn } from "../../../../lib/utils";
import { useIsMobile } from "../../../../hooks/use-mobile";
import { useRedirect, useParams, useLocation, usePopulatePathname } from 'blade/hooks';
import { useSharedSidebarState } from '../../../../stores/sidebar-store.client';

import { CollapseBarIcon } from '../../../ui/icons';
import { LogoutSection } from '../../shared/user-nav-content';

import {
  GalleryVerticalEnd,
  SquareTerminal,
  Bot,
  BookOpen,
  Settings2,
  Frame,
  PieChart,
  Map,
} from "lucide-react";
import { Link } from 'blade/client/components';
import { createPortal } from 'react-dom';
import { MobileHorizontalToolbar } from './mobile-horizontal-toolbar.client';


interface AppSidebarProps extends ComponentProps<"div"> {
  flyout: string | null;
  setFlyout: (flyout: string | null) => void;
  onSearchToggle?: (isSearchActive: boolean) => void;
  searchValue?: string;
  onSearchValueChange?: (value: string) => void;
  externalSearchActive?: boolean; // Add this line
  currentPlaceholder?: string; // Add this line
  currentMode?: string; // Add this line
  currentStep?: string; // Add this line
  formSubmitHandler?: (() => void) | null; // Add this line
}

interface IconItem {
  id: string;
  icon: any;
  label: string;
  tooltip: string;
  section: number;
  url?: string; // Optional URL for navigation
  flyoutHeaderStyle?: "title" | "button"; // Header style for flyouts
}

// Page navigation items for the dropdown
interface PageNavItem {
  id: string;
  label: string;
  icon: any;
  url: string;
  description?: string;
}



export function AppSidebar({
  flyout,
  setFlyout,
  onSearchToggle,
  searchValue = '',
  onSearchValueChange,
  externalSearchActive = false, // Add this line
  currentPlaceholder = 'Search commands', // Add this line
  currentMode = 'default', // Add this line
  currentStep = undefined, // Add this line
  formSubmitHandler = null, // Add this line
  className,
  ...props
}: AppSidebarProps) {
  const isMobile = useIsMobile();
  const { isRightSidebarOpen } = useSharedSidebarState();
  const redirect = useRedirect();
  const params = useParams();
  const location = useLocation();
  const populatePathname = usePopulatePathname();

 

  // Simplified toggle handler without debouncing - matches working right-sidebar pattern
  const handleToggleFlyout = useCallback((itemId: string) => {
    console.log(`🎯 Toggle flyout for ${itemId}:`, { currentFlyout: flyout, itemId });
    const newFlyout = flyout === itemId ? null : itemId;
    console.log(`🎯 Setting flyout to:`, newFlyout);
    setFlyout(newFlyout);
  }, [flyout, setFlyout]);


  // PERFORMANCE FIX: Memoize data object to prevent unnecessary re-renders and Blade revalidations
  const data = useMemo(() => ({
    user: {
      name: "School Admin",
      email: "<EMAIL>",
      avatar: "/avatars/admin.jpg",
    },
    teams: [
      {
        name: "School District",
        logo: GalleryVerticalEnd,
        plan: "Education",
      },
    ],
    navMain: [
      {
        title: "Dashboard",
        url: "#",
        icon: SquareTerminal,
        isActive: true,
        items: [
          {
            title: "Overview",
            url: "#",
          },
          {
            title: "Analytics",
            url: "#",
          },
          {
            title: "Reports",
            url: "#",
          },
        ],
      },
      {
        title: "Students",
        url: "#",
        icon: Bot,
        items: [
          {
            title: "All Students",
            url: "#",
          },
          {
            title: "Enrollment",
            url: "#",
          },
          {
            title: "Attendance",
            url: "#",
          },
        ],
      },
      {
        title: "Teachers",
        url: "#",
        icon: BookOpen,
        items: [
          {
            title: "All Teachers",
            url: "#",
          },
          {
            title: "Schedules",
            url: "#",
          },
          {
            title: "Performance",
            url: "#",
          },
        ],
      },
      {
        title: "Settings",
        url: "#",
        icon: Settings2,
        items: [
          {
            title: "General",
            url: "#",
          },
          {
            title: "Users",
            url: "#",
          },
          {
            title: "Security",
            url: "#",
          },
        ],
      },
    ],
    projects: [
      {
        name: "Academic Year 2024",
        url: "#",
        icon: Frame,
      },
      {
        name: "Summer Programs",
        url: "#",
        icon: PieChart,
      },
      {
        name: "Extracurriculars",
        url: "#",
        icon: Map,
      },
    ],
  }), []); // Empty dependency array since this is static data

const createRenderFlyoutContent = useCallback((setCommandMenuHeight: (height: number) => void) => {
  return (flyout: string | null, data: any, setFlyout: (flyout: string | null) => void) => {
    if (!flyout) return null;

    const currentPath = populatePathname(location.pathname);
    const slug = Array.isArray(params['slug']) ? params['slug'][0] : params['slug'];

    const isOnStudentsPage = currentPath === `/teacher/${slug}/students`;
    const isOnClassesPage = currentPath === `/teacher/${slug}/classes`;
    const isOnHomePage = currentPath === `/teacher/${slug}` || currentPath === `/teacher/${slug}/`;

    console.log('🔍 Rendering flyout content:', {
      flyout,
      currentPath,
      isOnStudentsPage,
      isOnClassesPage,
      isOnHomePage
    });

    switch (flyout) {
      case "home":
        return null;

      case "calendar":
        return (
          <div className="flex py-2 flex-col h-full">
            <div className="p-2 px-4 border-b border-black/20 dark:border-white/20">
              <div className="flex items-center justify-between mb-3">
                <h2 className="text-xl text-left font-manrope_1 font-semibold text-black/80 dark:text-white/80">
                  Calendar
                </h2>
                <FlyoutTrigger className="" setFlyout={setFlyout} />
              </div>
              <p className="text-sm text-black/60 dark:text-white/60 mb-4">
                View your schedule and upcoming events
              </p>
              <Link href="/teacher/calendar" prefetch={false}>
                <a className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 w-full">
                  Go to Calendar Page
                </a>
              </Link>
            </div>
          </div>
        );


      default:
        return (
          <div className="flex items-center justify-center h-full">
            <p className="text-muted-foreground">Select a panel</p>
          </div>
        );
    }
  };
}, [location.pathname, params, redirect, populatePathname]);

// Add this new component to properly manage search state


  // Only return MobileHorizontalToolbar - no desktop left sidebar
  return (
    <div className="flex h-full">

      {/* Mobile Horizontal Toolbar */}
<MobileHorizontalToolbar
  flyout={flyout}
  setFlyout={setFlyout}
  data={data}
  handleToggleFlyout={handleToggleFlyout}
  createRenderFlyoutContent={createRenderFlyoutContent}
  onSearchToggle={onSearchToggle}
  searchValue={searchValue}
  onSearchValueChange={onSearchValueChange}
  externalSearchActive={externalSearchActive} // Add this line
  currentPlaceholder={currentPlaceholder} // Add this line
  currentMode={currentMode} // Add this line
  currentStep={currentStep} // Add this line
  formSubmitHandler={formSubmitHandler} // Add this line
/>
    </div>
  );
}

// Custom FlyoutTrigger that closes the flyout but keeps IconButton active
const FlyoutTrigger = ({
  className,
  setFlyout
}: {
  className?: string;
  setFlyout: (flyout: string | null) => void;
}) => {
  return (
    <button
      onClick={() => setFlyout(null)} // Close flyout but keep IconButton active
      className={cn(
        "inline-flex items-center justify-center gap-2 w-4 h-4 text-black/60 dark:text-white/60 hover:text-black/90 dark:hover:text-white/90",
        className
      )}
    >
     <CollapseBarIcon/>
    </button>
  );
};