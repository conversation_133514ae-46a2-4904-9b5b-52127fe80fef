'use client';

import React from 'react';
import { Bell } from 'lucide-react';

export function NotificationsSection() {
  return (
    <div className="p-4 rounded-xl bg-gradient-to-b from-[#f8f8f8] via-[#f8f8f8] to-[#f0f0f0] shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(229_229_229)_inset,0_0.5px_0_1.5px_#d4d4d8_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] transition-all duration-200 overflow-hidden">
      <div className="flex items-center gap-3 mb-3">
        <Bell className="h-5 w-5 text-orange-500" />
        <h4 className="font-medium font-manrope_1 text-black/90 dark:text-white/90">Recent Notifications</h4>
      </div>
      <div className="space-y-2">
        <div className="p-2 bg-black/5 dark:bg-white/5 rounded text-sm">
          <p className="font-medium">System Update</p>
          <p className="text-black/70 dark:text-white/70 text-xs">2 hours ago</p>
        </div>
        <div className="p-2 bg-black/5 dark:bg-white/5 rounded text-sm">
          <p className="font-medium">New Message</p>
          <p className="text-black/70 dark:text-white/70 text-xs">1 day ago</p>
        </div>
        <div className="p-2 bg-black/5 dark:bg-white/5 rounded text-sm">
          <p className="font-medium">Assignment Due</p>
          <p className="text-black/70 dark:text-white/70 text-xs">3 days ago</p>
        </div>
      </div>
    </div>
  );
}