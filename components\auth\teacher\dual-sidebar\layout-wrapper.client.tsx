// auth/teacher/dual-sidebar/layout-wrapper.client.tsx
'use client';
import type { ReactNode } from 'react';
import { EnhancedSidebar } from './enhanced-sidebar.client';
import { SidebarProvider } from '../../../../stores/sidebar-store.client';

interface LayoutWrapperProps {
  children: ReactNode;
  showUserNav?: boolean;
  showHeader?: boolean;
  className?: string;
}

/**
 * LayoutWrapper - Similar to TanStack Start's _root.tsx
 * 
 * This component wraps the entire application with the dual sidebar layout.
 * It provides the same functionality as the TanStack Start root component
 * but adapted for Blade framework.
 * 
 * Note: ThemeProvider is now provided at the root level, so we don't need it here
 */
export function LayoutWrapper({ 
  children, 
  showUserNav = true, 
  showHeader = true,
  className 
}: LayoutWrapperProps) {
  return (
    <SidebarProvider>
      <EnhancedSidebar 
        showUserNav={showUserNav}
        showHeader={showHeader}
        className={className}
      >
        {children}
      </EnhancedSidebar>
    </SidebarProvider>
  );
}

export default LayoutWrapper;