// pages/teacher/[slug]/classes.tsx
import { use, useBatch } from 'blade/server/hooks';
import { useParams } from 'blade/hooks';
import TeacherClassesPageWithData from '../../../components/teacher/TeacherClassesPageWithData.client';

// Define types to fix implicit 'any' errors
interface User {
  id: string;
  name: string;
  email: string;
  slug: string;
  role: string;
  teacherId?: string;
  username?: string;
  classId?: string;
  grade?: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface ClassItem {
  id: string;
  name: string;
  description?: string;
  teacherId: string;
  schoolId?: string;
  subjectId?: string;
  gradeLevel?: string;
  maxCapacity?: number;
  currentEnrollment?: number;
  isActive?: boolean;
  startDate?: string;
  endDate?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface GradeLevel {
  id: string;
  name: string;
  code?: string;
  description?: string;
  category: string;
  educationType: string;
  teacherId: string;
  sortOrder?: number;
  isActive?: boolean;
}

interface EducationalContext {
  id: string;
  name: string;
  teacherId: string;
  isActive?: boolean;
  description?: string;
  createdAt?: string;
}

interface StudentClassAssignment {
  id: string;
  studentId: string;
  classId: string;
  enrolledAt: string;
  status: string;
}

const TeachersClassesPage = () => {
  const { slug } = useParams();

  // OPTIMIZED: Single batched query instead of multiple separate queries
  // This prevents the excessive revalidation and matches the working students page pattern
  const [allUsers, allClasses, allGradeLevels, allEducationalContexts, allStudents, allStudentClassAssignments, allTeachers] = useBatch(() => [
    // Get ALL users (teachers and students) in one query
    use.users({
      where: {
        OR: [
          { role: 'teacher' },
          { role: 'student' }
        ]
      }
    }),

    // Get ALL classes
    use.classes({
      where: { isActive: true }
    }),

    // Get ALL grade levels
    use.gradeLevels({
      where: { isActive: true },
      orderedBy: { ascending: ['sortOrder', 'name'] }
    }),

    // Get ALL educational contexts
    use.educationalContexts({
      where: { isActive: true }
    }),

    // Get ALL student users
    use.users({
      where: { role: 'student' },
      selecting: ['id', 'name', 'email', 'username', 'grade', 'isActive', 'role']
    }),

    // Get ALL student-class assignments
    use.studentClasses({
      selecting: ['id', 'studentId', 'classId', 'enrolledAt', 'status']
    }),

    // Get ALL teacher records (needed for matching)
    use.teachers()
  ]);

  // Find the current teacher user
  const teacher = allUsers.find((u: User) => u.slug === slug && u.role === 'teacher');

  if (!teacher) {
    return null; // Or a loading/error state
  }

  // Find the Teacher record for this user (needed for class relationships)
  const teacherRecord = allTeachers.find((t: any) => t.userId === teacher.id);

  if (!teacherRecord) {
    console.error('Teacher record not found for user:', teacher.id);
    return null;
  }

  // Filter data for this specific teacher using Teacher record ID
  const teacherClasses = allClasses.filter((classItem: ClassItem) =>
    classItem.teacherId === teacherRecord.id && classItem.isActive !== false
  );

  const teacherGradeLevels = allGradeLevels.filter((grade: GradeLevel) =>
    grade.teacherId === teacher.id && grade.isActive !== false
  );

  const teacherEducationalContexts = allEducationalContexts.filter((context: EducationalContext) =>
    context.teacherId === teacher.id && context.isActive !== false
  );

  const teacherStudents = allStudents.filter((student: User) => {
    return student.isActive !== false && student.role === 'student';
  });

  const teacherStudentClassAssignments = allStudentClassAssignments;

  return (
    <TeacherClassesPageWithData
      allClasses={teacherClasses}
      allGradeLevels={teacherGradeLevels}
      allEducationalContexts={teacherEducationalContexts}
      allStudents={teacherStudents}
      allStudentClassAssignments={teacherStudentClassAssignments}
      teacher={teacher}
    />
  );
};

export default TeachersClassesPage;
