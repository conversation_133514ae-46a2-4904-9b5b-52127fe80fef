// hooks/use-auto-hide-scrollbar.ts
import { useEffect, useRef } from 'react';

export const useAutoHideScrollbar = (scrollRef: React.RefObject<HTMLElement | null>) => {
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const scrollElement = scrollRef.current;
    if (!scrollElement) return;

    const handleScrollStart = () => {
      // Immediately show scrollbar when scrolling starts
      scrollElement.classList.add('scrolling');
      
      // Clear any existing timeout
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };

    const handleScrollEnd = () => {
      // Set timeout to hide scrollbar after scrolling stops
      scrollTimeoutRef.current = setTimeout(() => {
        scrollElement.classList.remove('scrolling');
      }, 1500); // Hide after 1.5 seconds of inactivity
    };

    const handleScroll = () => {
      handleScrollStart();
      handleScrollEnd();
    };

    // Add scroll event listener with passive for better performance
    scrollElement.addEventListener('scroll', handleScroll, { passive: true });

    // Cleanup function
    return () => {
      scrollElement.removeEventListener('scroll', handleScroll);
      
      // Clear any pending timeout
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
      
      // Ensure scrolling class is removed on cleanup
      scrollElement.classList.remove('scrolling');
    };
  }, [scrollRef]);
};