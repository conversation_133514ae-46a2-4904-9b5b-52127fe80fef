//components/dialog/DialogManager.client.tsx
'use client';

import { useEffect, useRef } from 'react';
import { useAttention } from 'react-attention';
import { useDialogStore } from '../../lib/stores/dialog.store';
import { StudentManagementDialog } from '../auth/teacher/dialogs/student-management-dialog.client';
import { GradeLevelManagementDialog } from '../auth/teacher/dialogs/grade-level-management-dialog.client';

interface ClassItem {
  id: string;
  name: string;
  description?: string;
  teacherId: string;
  schoolId?: string;
  subjectId?: string;
  gradeLevel?: string;
  maxCapacity?: number;
  currentEnrollment?: number;
  isActive?: boolean;
  startDate?: string;
  endDate?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface DialogManagerProps {
  teacherClasses?: ClassItem[];
}

// Hook for managing dialog state with react-attention
export function useDialogManager() {
  const {
    openStudentDialog,
    openGradeLevelDialog,
    closeStudentDialog,
    closeGradeLevelDialog,
    closeAllDialogs,
  } = useDialogStore();

  return {
    openStudentDialog,
    openGradeLevelDialog,
    closeStudentDialog,
    closeGradeLevelDialog,
    closeAllDialogs,
  };
}

export function DialogManager({ teacherClasses = [] }: DialogManagerProps) {
  const {
    isStudentDialogOpen,
    isGradeLevelDialogOpen,
    closeStudentDialog,
    closeGradeLevelDialog,
    activeDialogs
  } = useDialogStore();
  const studentDialogRef = useRef(null);
  const gradeLevelDialogRef = useRef(null);

  // Debug logging
  useEffect(() => {
    console.log('🎯 DialogManager: Active dialogs:', activeDialogs);
    console.log('🎯 DialogManager: Student dialog open:', isStudentDialogOpen);
    console.log('🎯 DialogManager: Grade level dialog open:', isGradeLevelDialogOpen);
  }, [activeDialogs, isStudentDialogOpen, isGradeLevelDialogOpen]);

  // Use react-attention for individual dialog management (not exclusive)
  useAttention(isStudentDialogOpen, closeStudentDialog, studentDialogRef);
  useAttention(isGradeLevelDialogOpen, closeGradeLevelDialog, gradeLevelDialogRef);

  return (
    <>
      {/* Student Management Dialog - Base layer (z-index: 50) */}
      <StudentManagementDialog
        isOpen={isStudentDialogOpen}
        onOpenChange={(open) => open ? null : closeStudentDialog()}
        teacherClasses={teacherClasses}
        ref={studentDialogRef}
      />

      {/* Grade Level Management Dialog - Top layer (z-index: 60) */}
      <GradeLevelManagementDialog
        isOpen={isGradeLevelDialogOpen}
        onOpenChange={(open) => open ? null : closeGradeLevelDialog()}
        ref={gradeLevelDialogRef}
      />
    </>
  );
}