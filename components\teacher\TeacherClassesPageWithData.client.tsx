// components/teacher/TeacherClassesPageWithData.client.tsx
'use client';

import { useMemo } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { TeacherAuthGuard } from '../auth/AuthGuard.client';
import ClassManagementTabsWithData from '../auth/teacher/classes/class-management-tabs-with-data.client';
import NoiseText from '../home/<USER>';

interface ClassItem {
  id: string;
  name: string;
  description?: string;
  teacherId: string;
  schoolId?: string;
  subjectId?: string;
  gradeLevel?: string;
  maxCapacity?: number;
  currentEnrollment?: number;
  isActive?: boolean;
  startDate?: string;
  endDate?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface GradeLevel {
  id: string;
  name: string;
  code?: string;
  description?: string;
  category: string;
  educationType: string;
  teacherId: string;
  schoolId?: string;
  sortOrder?: number;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface EducationalContext {
  id: string;
  name: string;
  type: string;
  description?: string;
  defaultGradeLevels?: string;
  teacherId: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface Teacher {
  id: string;
  name: string;
  email: string;
  slug: string;
}

interface Student {
  id: string;
  name: string;
  email: string;
  username?: string;
  grade?: string;
  isActive?: boolean;
}

interface StudentClassAssignment {
  id: string;
  studentId: string;
  classId: string;
  enrolledAt: string;
  status: string;
}

interface TeacherClassesPageWithDataProps {
  allClasses: ClassItem[];
  allGradeLevels: GradeLevel[];
  allEducationalContexts: EducationalContext[];
  allStudents: Student[];
  allStudentClassAssignments: StudentClassAssignment[];
  teacher?: Teacher; // Optional teacher prop to avoid double filtering (fallback to user if not provided)
}

const TeacherClassesPageWithData = ({
  allClasses,
  allGradeLevels,
  allEducationalContexts,
  allStudents,
  allStudentClassAssignments,
  teacher: teacherProp
}: TeacherClassesPageWithDataProps) => {
  const { user } = useAuth();

  // Early return if user is not available
  if (!user) {
    return null; // AuthGuard will handle redirect
  }

  // Use passed data directly if teacher prop is provided (data is already filtered)
  // Otherwise, fall back to filtering (for backward compatibility)
  const teacherClasses = useMemo(() => {
    if (teacherProp) {
      // Data is already filtered, use as-is
      return allClasses;
    }
    // Fallback: filter by user ID (this is incorrect for new classes but kept for compatibility)
    return allClasses.filter(classItem => classItem.teacherId === user.id);
  }, [allClasses, user.id, teacherProp]);

  const teacherGradeLevels = useMemo(() => {
    if (teacherProp) {
      // Data is already filtered, use as-is
      return allGradeLevels;
    }
    // Fallback: filter by user ID
    return allGradeLevels.filter(grade => grade.teacherId === user.id);
  }, [allGradeLevels, user.id, teacherProp]);

  const teacherEducationalContexts = useMemo(() => {
    if (teacherProp) {
      // Data is already filtered, use as-is
      return allEducationalContexts;
    }
    // Fallback: filter by user ID
    return allEducationalContexts.filter(context => context.teacherId === user.id);
  }, [allEducationalContexts, user.id, teacherProp]);

  // Get teacher's students (students created by this teacher)
  const teacherStudents = useMemo(() => {
    // For now, return all students - in a real app, you'd filter by teacher relationship
    // This could be done via a StudentTeacher relationship table or by checking who created the student
    return allStudents.filter(student => student.isActive);
  }, [allStudents]);

  // Get student-class assignments for teacher's classes
  const teacherStudentClassAssignments = useMemo(() => {
    const teacherClassIds = teacherClasses.map(c => c.id);
    return allStudentClassAssignments.filter(assignment =>
      teacherClassIds.includes(assignment.classId)
    );
  }, [allStudentClassAssignments, teacherClasses]);

  // Use the passed teacher prop if available, otherwise create from user
  const teacher: Teacher = teacherProp || {
    id: user.id,
    name: user.name,
    email: user.email,
    slug: user.slug
  };

  return (
    <TeacherAuthGuard>
      <div className="p-2">
        {/* Header */}
          <NoiseText
            text="Classes & Grades"
            className="text-xl md:text-2xl lg:text-2xl font-bold text-left"
          />
          <p className="text-left font-manrope_1 text-xs md:text-sm text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Manage your classes, grade levels, and educational contexts. Create flexible grading systems
            that work for traditional schools, vocational training, or professional certification programs.
          </p>

        {/* Class Management Component */}
        <ClassManagementTabsWithData
          classes={teacherClasses}
          students={teacherStudents}
          studentClassAssignments={teacherStudentClassAssignments}
          teacher={teacher}
        />
      </div>
    </TeacherAuthGuard>
  );
};

export default TeacherClassesPageWithData;
