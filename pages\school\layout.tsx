// pages/school/layout.tsx (Server Component - NO hooks allowed)
import { LayoutWrapper } from '../../components/auth/school/dual-sidebar';

const SchoolLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <AttentionProvider>
      <LayoutWrapper
        showUserNav={true}
        showHeader={true}
      >
        {children}
      </LayoutWrapper>
    </AttentionProvider>
  );
};

export default SchoolLayout;