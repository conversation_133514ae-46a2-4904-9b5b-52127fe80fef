# Blade.im Data Flow Optimization Guide

## Problem Solved
Your application was experiencing excessive revalidation (every second instead of every 5 seconds) due to:
1. **Multiple separate queries** instead of batched queries
2. **Client-side data fetching** creating separate revalidation cycles
3. **Unstable query keys** causing new queries on each render
4. **Duplicated teacher-student relationship queries** across components

## Solution Implemented

### 1. React-Attention Package Integration
- **Added AttentionProvider** to root layout (`pages/layout.tsx`)
- **Created centralized DialogManager** (`components/dialogs/DialogManager.client.tsx`)
- **Ensures only one dialog is open at a time** across the entire application
- **Prevents UI conflicts** and improves user experience

### 2. Data Flow Optimization

#### Before (Problematic):
```typescript
// Multiple separate queries causing excessive revalidation
const users = use.users();
const gradeLevels = use.gradeLevels();
const classes = use.classes();
const studentTeachers = use.studentTeachers();
```

#### After (Optimized):
```typescript
// Single batched query - prevents excessive revalidation
const [users, gradeLevels, classes, studentTeachers] = useBatch(() => [
  use.users({ where: { OR: [{ role: 'teacher' }, { role: 'student' }] } }),
  use.gradeLevels({ where: { isActive: true } }),
  use.classes({ where: { isActive: true } }),
  use.studentTeachers()
]);
```

### 3. Component Architecture

#### Server Components (Data Fetching)
- `pages/teacher/[slug]/students-optimized.tsx` - Batched student data
- `pages/teacher/[slug]/classes-optimized.tsx` - Batched class data
- `components/optimization/DataOptimizer.server.tsx` - Reusable data provider

#### Client Components (Interactions Only)
- `components/dialogs/DialogManager.client.tsx` - Dialog state management
- `components/auth/teacher/dual-sidebar/enhanced-sidebar.client.tsx` - UI interactions
- All existing dialog components - Keep for mutations only

### 4. Key Optimizations Applied

#### A. Batched Queries
```typescript
// Instead of 4 separate queries, use 1 batched query
const [teacherData, studentsData, classesData, gradeLevelsData] = useBatch(() => [
  use.users({ with: { teacherSubjects: { with: { subject: true } } } }),
  use.studentTeachers({ with: { student: { with: { user: true } } } }),
  use.classes({ with: { studentClasses: { with: { student: true } } } }),
  use.gradeLevels({ orderedBy: { ascending: ['sortOrder', 'name'] } })
]);
```

#### B. Server-Side Data Fetching
- **ALL data queries moved to server components**
- **Client components only handle user interactions and mutations**
- **Data passed down as props** - no client-side queries

#### C. Centralized Dialog Management
```typescript
// Single source of truth for all dialogs
export function useDialogManager() {
  const [studentDialogVisible, setStudentDialogVisible] = useState(false);
  const [gradeLevelDialogVisible, setGradeLevelDialogVisible] = useState(false);

  // React-attention ensures only one dialog open at a time
  useAttention(studentDialogVisible, () => setStudentDialogVisible(false));
  useAttention(gradeLevelDialogVisible, () => setGradeLevelDialogVisible(false));
  
  // ... rest of logic
}
```

#### D. Quick Action Button
- **Added "Students" button** to enhanced sidebar header
- **Uses react-attention** to open dialogs from any page
- **Centralized access** to student management functionality

## Implementation Steps

### Step 1: Replace Your Current Pages
Replace your existing pages with the optimized versions:

```bash
# Backup existing pages
mv pages/teacher/[slug]/students.tsx pages/teacher/[slug]/students-backup.tsx
mv pages/teacher/[slug]/classes.tsx pages/teacher/[slug]/classes-backup.tsx

# Use optimized versions
mv pages/teacher/[slug]/students-optimized.tsx pages/teacher/[slug]/students.tsx
mv pages/teacher/[slug]/classes-optimized.tsx pages/teacher/[slug]/classes.tsx
```

### Step 2: Update Your Components
The following components have been optimized:
- ✅ `pages/layout.tsx` - Added AttentionProvider
- ✅ `components/dialogs/DialogManager.client.tsx` - Centralized dialog management
- ✅ `components/auth/teacher/dual-sidebar/enhanced-sidebar.client.tsx` - Added quick action button

### Step 3: Test the Optimization
1. **Navigate to teacher pages** - should load faster
2. **Check browser network tab** - fewer queries
3. **Monitor revalidation** - should be every 5 seconds, not every second
4. **Test dialog functionality** - only one dialog open at a time

## Expected Results

### Performance Improvements
- **Reduced query frequency**: From every second to every 5 seconds
- **Fewer network requests**: Batched queries instead of separate ones
- **Faster page loads**: Single transaction instead of multiple queries
- **Better UX**: Only one dialog open at a time

### Data Flow Benefits
- **Predictable revalidation**: Follows Blade's 5-second cycle
- **Reduced server load**: Fewer database queries
- **Improved caching**: Better query deduplication
- **Cleaner architecture**: Clear separation of server/client concerns

## Monitoring and Debugging

### Check Revalidation Frequency
```javascript
// Add to any client component to monitor revalidation
useEffect(() => {
  console.log('Component revalidated at:', new Date().toISOString());
}, [data]); // Replace 'data' with your actual data dependency
```

### Verify Batched Queries
- Open browser DevTools → Network tab
- Navigate to teacher pages
- Should see fewer, larger requests instead of many small ones

### Dialog State Debugging
```javascript
// The DialogManager already includes debugging
console.log('Dialog state:', { studentDialogVisible, gradeLevelDialogVisible });
```

## Best Practices Going Forward

### 1. Always Batch Related Queries
```typescript
// ✅ Good - Batched
const [users, classes] = useBatch(() => [
  use.users(),
  use.classes()
]);

// ❌ Bad - Separate queries
const users = use.users();
const classes = use.classes();
```

### 2. Server Components for Data, Client for Interactions
```typescript
// ✅ Server Component - Data fetching
export default function TeacherPage() {
  const data = use.teachers();
  return <TeacherClient data={data} />;
}

// ✅ Client Component - User interactions
export function TeacherClient({ data }) {
  const { add } = useMutation();
  // Only mutations and UI state here
}
```

### 3. Use React-Attention for Overlays
```typescript
// ✅ Good - Prevents multiple dialogs
useAttention(isVisible, () => setIsVisible(false));

// ❌ Bad - Multiple dialogs can conflict
const [dialog1Open, setDialog1Open] = useState(false);
const [dialog2Open, setDialog2Open] = useState(false);
```

## Troubleshooting

### If Revalidation is Still Too Frequent
1. Check for client-side queries in components
2. Verify all related queries are batched
3. Look for unstable query keys (changing on each render)

### If Dialogs Don't Work
1. Ensure AttentionProvider is in root layout
2. Check DialogManager is included in pages
3. Verify useDialogManager hook is used correctly

### If Data Seems Stale
1. Blade automatically revalidates every 5 seconds
2. Mutations trigger immediate revalidation
3. Check network tab for actual query frequency

## Files Modified/Created

### New Files
- `components/dialogs/DialogManager.client.tsx`
- `components/optimization/DataOptimizer.server.tsx`
- `pages/teacher/[slug]/students-optimized.tsx`
- `pages/teacher/[slug]/classes-optimized.tsx`

### Modified Files
- `pages/layout.tsx` - Added AttentionProvider
- `components/auth/teacher/dual-sidebar/enhanced-sidebar.client.tsx` - Added dialog button

This optimization should resolve your revalidation issues and provide a much better user experience with centralized dialog management!