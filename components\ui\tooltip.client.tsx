"use client";

import * as React from 'react';
import { createPortal } from 'react-dom';
import {
  motion,
  AnimatePresence,
  LayoutGroup,
  type Transition,
} from 'motion/react';
import type { ComponentProps } from "react";
import { Tooltip } from '@base-ui-components/react/tooltip';
import { cn } from "../../lib/utils";

// Types for animated tooltip functionality
type Side = 'top' | 'bottom' | 'left' | 'right';
type Align = 'start' | 'center' | 'end';

type AnimatedTooltipData = {
  content: React.ReactNode;
  rect: DOMRect;
  side: Side;
  sideOffset: number;
  align: Align;
  alignOffset: number;
  id: string;
  arrow: boolean;
};

type AnimatedTooltipContextType = {
  showAnimatedTooltip: (data: AnimatedTooltipData) => void;
  hideAnimatedTooltip: () => void;
  currentAnimatedTooltip: AnimatedTooltipData | null;
  transition: Transition;
  globalId: string;
  isAnimatedMode: boolean;
};

const AnimatedTooltipContext = React.createContext<AnimatedTooltipContextType | undefined>(undefined);

const useAnimatedTooltip = () => {
  const context = React.useContext(AnimatedTooltipContext);
  return context; // Can be undefined if not in animated mode
};

// Position calculation for animated tooltips
function getAnimatedTooltipPosition({
  rect,
  side,
  sideOffset,
  align,
  alignOffset,
}: {
  rect: DOMRect;
  side: Side;
  sideOffset: number;
  align: Align;
  alignOffset: number;
}) {
  switch (side) {
    case 'top':
      if (align === 'start') {
        return {
          x: rect.left + alignOffset,
          y: rect.top - sideOffset,
          transform: 'translate(0, -100%)',
          initial: { y: 15 },
        };
      } else if (align === 'end') {
        return {
          x: rect.right + alignOffset,
          y: rect.top - sideOffset,
          transform: 'translate(-100%, -100%)',
          initial: { y: 15 },
        };
      } else {
        return {
          x: rect.left + rect.width / 2,
          y: rect.top - sideOffset,
          transform: 'translate(-50%, -100%)',
          initial: { y: 15 },
        };
      }
    case 'bottom':
      if (align === 'start') {
        return {
          x: rect.left + alignOffset,
          y: rect.bottom + sideOffset,
          transform: 'translate(0, 0)',
          initial: { y: -15 },
        };
      } else if (align === 'end') {
        return {
          x: rect.right + alignOffset,
          y: rect.bottom + sideOffset,
          transform: 'translate(-100%, 0)',
          initial: { y: -15 },
        };
      } else {
        return {
          x: rect.left + rect.width / 2,
          y: rect.bottom + sideOffset,
          transform: 'translate(-50%, 0)',
          initial: { y: -15 },
        };
      }
    case 'left':
      if (align === 'start') {
        return {
          x: rect.left - sideOffset,
          y: rect.top + alignOffset,
          transform: 'translate(-100%, 0)',
          initial: { x: 15 },
        };
      } else if (align === 'end') {
        return {
          x: rect.left - sideOffset,
          y: rect.bottom + alignOffset,
          transform: 'translate(-100%, -100%)',
          initial: { x: 15 },
        };
      } else {
        return {
          x: rect.left - sideOffset,
          y: rect.top + rect.height / 2,
          transform: 'translate(-100%, -50%)',
          initial: { x: 15 },
        };
      }
    case 'right':
      if (align === 'start') {
        return {
          x: rect.right + sideOffset,
          y: rect.top + alignOffset,
          transform: 'translate(0, 0)',
          initial: { x: -15 },
        };
      } else if (align === 'end') {
        return {
          x: rect.right + sideOffset,
          y: rect.bottom + alignOffset,
          transform: 'translate(0, -100%)',
          initial: { x: -15 },
        };
      } else {
        return {
          x: rect.right + sideOffset,
          y: rect.top + rect.height / 2,
          transform: 'translate(0, -50%)',
          initial: { x: -15 },
        };
      }
  }
}

// Animated tooltip arrow component
function AnimatedTooltipArrow({ side }: { side: Side }) {
  return (
    <div
      className={cn(
        'absolute bg-primary z-50 size-2.5 rotate-45 rounded-[2px]',
        (side === 'top' || side === 'bottom') && 'left-1/2 -translate-x-1/2',
        (side === 'left' || side === 'right') && 'top-1/2 -translate-y-1/2',
        side === 'top' && '-bottom-[3px]',
        side === 'bottom' && '-top-[3px]',
        side === 'left' && '-right-[3px]',
        side === 'right' && '-left-[3px]',
      )}
    />
  );
}

// Animated tooltip portal
function AnimatedTooltipPortal({ children }: { children: React.ReactNode }) {
  const [isMounted, setIsMounted] = React.useState(false);
  React.useEffect(() => setIsMounted(true), []);
  return isMounted ? createPortal(children, document.body) : null;
}

// Animated tooltip overlay
function AnimatedTooltipOverlay() {
  const context = useAnimatedTooltip();
  if (!context) return null;
  
  const { currentAnimatedTooltip, transition, globalId } = context;

  const position = React.useMemo(() => {
    if (!currentAnimatedTooltip) return null;
    return getAnimatedTooltipPosition({
      rect: currentAnimatedTooltip.rect,
      side: currentAnimatedTooltip.side,
      sideOffset: currentAnimatedTooltip.sideOffset,
      align: currentAnimatedTooltip.align,
      alignOffset: currentAnimatedTooltip.alignOffset,
    });
  }, [currentAnimatedTooltip]);

  return (
    <AnimatePresence>
      {currentAnimatedTooltip && currentAnimatedTooltip.content && position && (
        <AnimatedTooltipPortal>
          <motion.div
            data-slot="animated-tooltip-overlay-container"
            className="fixed z-50"
            style={{
              top: position.y,
              left: position.x,
              transform: position.transform,
            }}
          >
            <motion.div
              data-slot="animated-tooltip-overlay"
              layoutId={`animated-tooltip-overlay-${globalId}`}
              initial={{ opacity: 0, scale: 0, ...position.initial }}
              animate={{ opacity: 1, scale: 1, x: 0, y: 0 }}
              exit={{ opacity: 0, scale: 0, ...position.initial }}
              transition={transition}
              className="relative rounded-full border border-black/10 dark:border-white/10 font-manrope text-black dark:text-white backdrop-blur-xs px-3 py-1.5 text-sm  w-fit text-balance"
            >
              {currentAnimatedTooltip.content}
              {currentAnimatedTooltip.arrow && (
                <AnimatedTooltipArrow side={currentAnimatedTooltip.side} />
              )}
            </motion.div>
          </motion.div>
        </AnimatedTooltipPortal>
      )}
    </AnimatePresence>
  );
}

// Enhanced TooltipProvider that supports both modes
function TooltipProvider({
  delay = 0,
  closeDelay = 0,
  timeout = 400,
  animated = false,
  openDelay = 700,
  animatedCloseDelay = 300,
  transition = { type: 'spring', stiffness: 300, damping: 25 },
  children,
  ...props
}: ComponentProps<typeof Tooltip.Provider> & {
  animated?: boolean;
  openDelay?: number;
  animatedCloseDelay?: number;
  transition?: Transition;
}) {
  const globalId = React.useId();
  const [currentAnimatedTooltip, setCurrentAnimatedTooltip] = React.useState<AnimatedTooltipData | null>(null);
  const timeoutRef = React.useRef<number | null>(null);
  const lastCloseTimeRef = React.useRef<number>(0);

  const showAnimatedTooltip = React.useCallback(
    (data: AnimatedTooltipData) => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
      if (currentAnimatedTooltip !== null) {
        setCurrentAnimatedTooltip(data);
        return;
      }
      const now = Date.now();
      const delayTime = now - lastCloseTimeRef.current < animatedCloseDelay ? 0 : openDelay;
      timeoutRef.current = window.setTimeout(() => setCurrentAnimatedTooltip(data), delayTime);
    },
    [openDelay, animatedCloseDelay, currentAnimatedTooltip],
  );

  const hideAnimatedTooltip = React.useCallback(() => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    timeoutRef.current = window.setTimeout(() => {
      setCurrentAnimatedTooltip(null);
      lastCloseTimeRef.current = Date.now();
    }, animatedCloseDelay);
  }, [animatedCloseDelay]);

  const hideImmediate = React.useCallback(() => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    setCurrentAnimatedTooltip(null);
    lastCloseTimeRef.current = Date.now();
  }, []);

  React.useEffect(() => {
    if (animated) {
      window.addEventListener('scroll', hideImmediate, true);
      return () => window.removeEventListener('scroll', hideImmediate, true);
    }
  }, [hideImmediate, animated]);

  const animatedContextValue = React.useMemo(() => ({
    showAnimatedTooltip,
    hideAnimatedTooltip,
    currentAnimatedTooltip,
    transition,
    globalId,
    isAnimatedMode: animated,
  }), [showAnimatedTooltip, hideAnimatedTooltip, currentAnimatedTooltip, transition, globalId, animated]);

  if (animated) {
    return (
      <AnimatedTooltipContext.Provider value={animatedContextValue}>
        <LayoutGroup>
          <Tooltip.Provider
            delay={delay}
            closeDelay={closeDelay}
            timeout={timeout}
            {...props}
          >
            {children}
          </Tooltip.Provider>
        </LayoutGroup>
        <AnimatedTooltipOverlay />
      </AnimatedTooltipContext.Provider>
    );
  }

  return (
    <Tooltip.Provider
      delay={delay}
      closeDelay={closeDelay}
      timeout={timeout}
      {...props}
    >
      {children}
    </Tooltip.Provider>
  );
}

function TooltipRoot({
  defaultOpen = false,
  delay = 600,
  closeDelay = 0,
  hoverable = true,
  ...props
}: ComponentProps<typeof Tooltip.Root>) {
  return (
    <Tooltip.Root
      defaultOpen={defaultOpen}
      delay={delay}
      closeDelay={closeDelay}
      hoverable={hoverable}
      {...props}
    />
  );
}

function TooltipTrigger({
  className,
  children,
  ...props
}: ComponentProps<typeof Tooltip.Trigger>) {
  const animatedContext = useAnimatedTooltip();
  
  if (animatedContext?.isAnimatedMode) {
    // Return children as-is for animated mode - they'll be wrapped by AnimatedTooltip
    return React.cloneElement(children as React.ReactElement, {
      className: cn(className, (children as React.ReactElement).props?.className),
      ...props,
    });
  }

  return (
    <Tooltip.Trigger
      className={cn(className)}
      {...props}
    >
      {children}
    </Tooltip.Trigger>
  );
}

function TooltipContent({
  className,
  sideOffset = 8,
  side = "top",
  align = "center",
  children,
  ...props
}: ComponentProps<typeof Tooltip.Popup> & {
  sideOffset?: number;
  side?: "top" | "bottom" | "left" | "right";
  align?: "start" | "center" | "end";
}) {
  const animatedContext = useAnimatedTooltip();
  
  if (animatedContext?.isAnimatedMode) {
    // In animated mode, content is handled by the overlay
    return null;
  }

  return (
    <Tooltip.Portal>
      <Tooltip.Positioner
        side={side}
        sideOffset={sideOffset}
        align={align}
      >
        <Tooltip.Popup
          className={cn(
            "z-50 overflow-hidden shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] rounded-full font-manrope text-black dark:text-white backdrop-blur-md px-3 py-1.5 text-xs animate-in fade-in-0 zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
            className
          )}
          {...props}
        >
          {children}
        </Tooltip.Popup>
      </Tooltip.Positioner>
    </Tooltip.Portal>
  );
}

// New AnimatedTooltip component for use within animated provider
function AnimatedTooltip({
  children,
  content,
  side = 'top',
  sideOffset = 14,
  align = 'center',
  alignOffset = 0,
  arrow = true,
}: {
  children: React.ReactElement;
  content: React.ReactNode;
  side?: Side;
  sideOffset?: number;
  align?: Align;
  alignOffset?: number;
  arrow?: boolean;
}) {
  const animatedContext = useAnimatedTooltip();
  const id = React.useId();
  const triggerRef = React.useRef<HTMLElement>(null);

  if (!animatedContext?.isAnimatedMode) {
    // Fallback to regular tooltip if not in animated mode
    return (
      <TooltipRoot>
        <TooltipTrigger>
          {children}
        </TooltipTrigger>
        <TooltipContent side={side} sideOffset={sideOffset} align={align}>
          {content}
        </TooltipContent>
      </TooltipRoot>
    );
  }

  const { showAnimatedTooltip, hideAnimatedTooltip } = animatedContext;

  const handleOpen = React.useCallback(() => {
    if (!triggerRef.current) return;
    const rect = triggerRef.current.getBoundingClientRect();
    showAnimatedTooltip({
      content,
      rect,
      side,
      sideOffset,
      align,
      alignOffset,
      id,
      arrow,
    });
  }, [showAnimatedTooltip, content, side, sideOffset, align, alignOffset, id, arrow]);

  const handleMouseEnter = React.useCallback(
    (e: React.MouseEvent<HTMLElement>) => {
      (children.props as React.HTMLAttributes<HTMLElement>)?.onMouseEnter?.(e);
      handleOpen();
    },
    [handleOpen, children.props],
  );

  const handleMouseLeave = React.useCallback(
    (e: React.MouseEvent<HTMLElement>) => {
      (children.props as React.HTMLAttributes<HTMLElement>)?.onMouseLeave?.(e);
      hideAnimatedTooltip();
    },
    [hideAnimatedTooltip, children.props],
  );

  const handleFocus = React.useCallback(
    (e: React.FocusEvent<HTMLElement>) => {
      (children.props as React.HTMLAttributes<HTMLElement>)?.onFocus?.(e);
      handleOpen();
    },
    [handleOpen, children.props],
  );

  const handleBlur = React.useCallback(
    (e: React.FocusEvent<HTMLElement>) => {
      (children.props as React.HTMLAttributes<HTMLElement>)?.onBlur?.(e);
      hideAnimatedTooltip();
    },
    [hideAnimatedTooltip, children.props],
  );

  // Handle ref forwarding for components that can't receive refs directly (like Next.js Link)
  const childWithRef = React.useMemo(() => {
    // Check if the child is a component that might not accept refs (like Link)
    const childType = (children as any).type;
    const isLinkComponent = childType?.displayName === 'Link' || 
                           childType?.name === 'Link' ||
                           typeof childType === 'object' && childType.$typeof;

    if (isLinkComponent) {
      // For Link components, wrap in a div to capture the ref
      return (
        <div
          ref={triggerRef}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          onFocus={handleFocus}
          onBlur={handleBlur}
          data-state="closed"
          data-side={side}
          data-align={align}
          data-slot="animated-tooltip-trigger"
          style={{ display: 'inline-block' }}
        >
          {children}
        </div>
      );
    }

    // For regular elements, clone with ref and handlers
    return React.cloneElement(children, {
      ref: triggerRef,
      onMouseEnter: handleMouseEnter,
      onMouseLeave: handleMouseLeave,
      onFocus: handleFocus,
      onBlur: handleBlur,
      'data-state': 'closed',
      'data-side': side,
      'data-align': align,
      'data-slot': 'animated-tooltip-trigger',
    } as React.HTMLAttributes<HTMLElement>);
  }, [children, handleMouseEnter, handleMouseLeave, handleFocus, handleBlur, side, align]);

  return childWithRef;
}

// Convenience wrapper that combines Root with the common structure
function TooltipWrapper({
  children,
  content,
  ...rootProps
}: ComponentProps<typeof Tooltip.Root> & {
  content: React.ReactNode;
  children: React.ReactNode;
}) {
  return (
    <TooltipRoot {...rootProps}>
      <TooltipTrigger>
        {children}
      </TooltipTrigger>
      <TooltipContent>
        {content}
      </TooltipContent>
    </TooltipRoot>
  );
}

export {
  TooltipProvider,
  TooltipRoot as Tooltip,
  TooltipTrigger,
  TooltipContent,
  TooltipWrapper,
  AnimatedTooltip,
};