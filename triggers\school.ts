﻿// triggers/school.ts
import type { AddTrigger, SetTrigger, RemoveTrigger } from 'blade/types';
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;



    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processSchoolData = (schoolData: any) => {
      console.log('School during.add trigger - processing data:', schoolData);

      // Set default values
      schoolData.isActive = schoolData.isActive !== false; // Default to true
      schoolData.studentCount = schoolData.studentCount || 0;
      schoolData.teacherCount = schoolData.teacherCount || 0;
      schoolData.createdAt = schoolData.createdAt || new Date();
      schoolData.updatedAt = schoolData.updatedAt || new Date();

      console.log('School during.add trigger - processed data:', schoolData);
      return schoolData;
    };

    // Handle array of schools
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processSchoolData);
    } else {
      // Handle single school
      typedQuery.with = processSchoolData(typedQuery.with);
    }

    return typedQuery;
};
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

   
    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // Update timestamp
    typedQuery.to.updatedAt = new Date();

    console.log('School during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
};
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  
    // Add any validation or cleanup logic for deletions
    console.log('School during.remove trigger called with query:', typedQuery);
    return typedQuery;
};
