'use client';

import { create } from 'zustand';

interface UserNavFlyoutState {
  isOpen: boolean;
  content: 'user-info' | 'settings' | 'notifications' | null;
  open: (content: 'user-info' | 'settings' | 'notifications') => void;
  close: () => void;
  toggle: (content: 'user-info' | 'settings' | 'notifications') => void;
}

export const useUserNavFlyoutStore = create<UserNavFlyoutState>((set, get) => ({
  isOpen: false,
  content: null,
  open: (content) => set({ isOpen: true, content }),
  close: () => set({ isOpen: false, content: null }),
  toggle: (content) => {
    const { isOpen, content: currentContent } = get();
    if (isOpen && currentContent === content) {
      set({ isOpen: false, content: null });
    } else {
      set({ isOpen: true, content });
    }
  },
}));
